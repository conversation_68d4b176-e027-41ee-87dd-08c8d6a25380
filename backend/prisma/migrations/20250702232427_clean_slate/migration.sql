-- CreateTable
CREATE TABLE "code_health_reports" (
    "id" SERIAL NOT NULL,
    "project_id" INTEGER NOT NULL,
    "overall_score" DOUBLE PRECISION NOT NULL,
    "complexity_score" DOUBLE PRECISION NOT NULL,
    "maintainability" DOUBLE PRECISION NOT NULL,
    "technical_debt" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "analysis_duration" INTEGER NOT NULL,
    "file_count" INTEGER NOT NULL,
    "ai_analysis_model" TEXT,

    CONSTRAINT "code_health_reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_metrics" (
    "id" SERIAL NOT NULL,
    "report_id" INTEGER NOT NULL,
    "file_path" TEXT NOT NULL,
    "complexity" DOUBLE PRECISION NOT NULL,
    "nesting_depth" INTEGER NOT NULL,
    "function_count" INTEGER NOT NULL,
    "line_count" INTEGER NOT NULL,
    "has_error_handling" BOOLEAN NOT NULL,
    "risk_level" TEXT NOT NULL,
    "issues" JSONB NOT NULL,

    CONSTRAINT "file_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "function_metrics" (
    "id" SERIAL NOT NULL,
    "file_metric_id" INTEGER NOT NULL,
    "function_name" TEXT NOT NULL,
    "start_line" INTEGER NOT NULL,
    "end_line" INTEGER NOT NULL,
    "complexity" DOUBLE PRECISION NOT NULL,
    "parameter_count" INTEGER NOT NULL,
    "return_count" INTEGER NOT NULL,
    "is_async" BOOLEAN NOT NULL,
    "has_error_handling" BOOLEAN NOT NULL,

    CONSTRAINT "function_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "code_patterns" (
    "id" SERIAL NOT NULL,
    "report_id" INTEGER NOT NULL,
    "pattern_type" TEXT NOT NULL,
    "severity" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "line_number" INTEGER,
    "description" TEXT NOT NULL,
    "suggestion" TEXT,

    CONSTRAINT "code_patterns_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "code_health_reports" ADD CONSTRAINT "code_health_reports_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_metrics" ADD CONSTRAINT "file_metrics_report_id_fkey" FOREIGN KEY ("report_id") REFERENCES "code_health_reports"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "function_metrics" ADD CONSTRAINT "function_metrics_file_metric_id_fkey" FOREIGN KEY ("file_metric_id") REFERENCES "file_metrics"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "code_patterns" ADD CONSTRAINT "code_patterns_report_id_fkey" FOREIGN KEY ("report_id") REFERENCES "code_health_reports"("id") ON DELETE CASCADE ON UPDATE CASCADE;
