-- CreateExtension
CREATE EXTENSION IF NOT EXISTS vector;

-- CreateTable
CREATE TABLE "Documentation" (
    "id" SERIAL NOT NULL,
    "path" TEXT NOT NULL,
    "unitName" TEXT NOT NULL,
    "unitType" TEXT NOT NULL,
    "purpose" TEXT NOT NULL,
    "humanReadableExplanation" TEXT NOT NULL,
    "visualDiagram" TEXT NOT NULL,
    "embedding" vector NOT NULL,
    "commit" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Documentation_pkey" PRIMARY KEY ("id")
);