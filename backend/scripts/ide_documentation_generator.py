#!/usr/bin/env python3

import os
import sys
import json
import subprocess
import re
import hashlib
import time
import argparse
from datetime import datetime
from dotenv import load_dotenv
import requests
from typing import List, Dict, Any, Tuple, Optional, Union

# Load environment variables
load_dotenv()

# --- Configuration & Constants ---
GENERATED_DOCS_BASE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'docs', 'generated', 'files')
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# Standard directories to exclude
DEFAULT_EXCLUDED_DIRS = {
    "node_modules", ".git", ".svn", "__pycache__", "venv", ".venv", "env",
    "dist", "build", "out", "target", "*.egg-info", ".vscode", ".idea",
    "vendor", "site-packages", ".terraform", ".serverless", "coverage",
    "logs", "temp", "tmp",
}

# Exclude common binary/non-code file extensions
EXCLUDED_EXTENSIONS = {
    # Common data/text formats
    '.csv', '.json', '.yaml', '.yml', '.xml','.txt', '.md', '.rst',
    # Binaries/Archives/Media/Docs/Fonts etc.
    '.bin', '.dat', '.db', '.sqlite', '.sqlite3',
    '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.tif', '.tiff', '.webp', '.svg',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt', '.ods', '.odp',
    '.zip', '.tar', '.gz', '.bz2', '.rar', '.7z', '.jar', '.war',
    '.mp3', '.wav', '.ogg', '.flac', '.mp4', '.avi', '.mov', '.wmv', '.mkv', '.webm',
    '.exe', '.dll', '.so', '.dylib', '.pyc', '.pyo', '.o', '.a', '.obj', '.class',
    '.lock', '.sum','.icns',
    '.ttf', '.otf', '.woff', '.woff2',
    '.log',
}

# --- Utility Functions ---
def log_error(message):
    """Log error messages to stderr."""
    print(message, file=sys.stderr)

def log_info(message):
    """Log informational messages to stderr."""
    print(message, file=sys.stderr)

# --- LLM Client ---
class LLMClient:
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL_NAME", "gemini-1.5-flash")
        if not self.api_key or "YOUR_GEMINI_API_KEY_HERE" in self.api_key:
            raise ValueError("GEMINI_API_KEY is not configured in .env file.")
        self.api_endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent?key={self.api_key}"

    def _build_prompt(self, file_path, file_content, code_snippet, unit_type):
        diagram_type = "'graph TD'" if unit_type == "function" else "'classDiagram'"
        diagram_instruction = f"A Mermaid.js {diagram_type} that visually represents the logic. Be accurate and detailed."

        return f"""
You are an expert software engineer AI. Your task is to analyze the provided code snippet and generate documentation in a structured JSON format.

Analyze the following `{unit_type}` snippet from the file at `{file_path}`.

Full File Content (for context):
```
{file_content}
```

Code Snippet to Analyze:
```
{code_snippet}
```

Generate a single, valid JSON object that documents the snippet. The JSON object must adhere to the following schema. Do not include any explanatory text, markdown formatting, or anything else before or after the JSON object.

JSON Schema:
{{
  "purpose": "A brief, one-sentence explanation of what this code does from a business or feature perspective.",
  "humanReadableExplanation": "A detailed explanation of the code's logic, its role, and how it works. Aimed at a new developer.",
  "dependencies": [{{"type": "internal|external", "name": "function/class/library name"}}],
  "inputs": [{{"name": "param_name", "type": "param_type", "description": "Parameter purpose."}}],
  "outputs": {{"type": "return_type", "description": "What this function returns.", "throws": ["Potential error types."]}},
  "visualDiagram": "{diagram_instruction}"
}}
"""

    def _build_diagram_prompt(self, code_snippet, documentation_json):
        return f"""
You are a software documentation expert specializing in visual representation. Your task is to create a Mermaid.js diagram that accurately illustrates the provided code snippet and its purpose.

Analyze the code and its accompanying documentation to determine the most appropriate diagram type (e.g., flowchart for logic, sequence diagram for interactions, class diagram for structure).

Code Snippet:
```
{code_snippet}
```

JSON Documentation (for context):
```json
{documentation_json}
```

Based on the analysis, generate ONLY the Mermaid.js syntax for the most suitable diagram. Do not include the `mermaid` code block markers or any other explanatory text. The output should be the raw Mermaid syntax.
"""

    def generate_diagram(self, code_snippet, documentation_json, max_retries=3, timeout=60):
        headers = {"Content-Type": "application/json"}
        prompt = self._build_diagram_prompt(code_snippet, json.dumps(documentation_json, indent=2))
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {"temperature": 0.1}
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_endpoint, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                response_data = response.json()
                diagram_syntax = response_data['candidates'][0]['content']['parts'][0]['text']
                return diagram_syntax.strip()
            except requests.exceptions.RequestException as e:
                log_error(f"Warning: Network error on diagram generation attempt {attempt + 1}: {e}")
                time.sleep(2 ** attempt)
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                log_error(f"Warning: Failed to parse Gemini response for diagram: {e}. Response: {response.text}")
                return "graph TD\n  A[Error Generating Diagram]"
        log_error(f"Error: Gemini API request for diagram failed after {max_retries} attempts.")
        return "graph TD\n  A[API Request Failed]"

    def generate_documentation(self, file_path, file_content, code_snippet, unit_type, max_retries=3, timeout=90):
        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": self._build_prompt(file_path, file_content, code_snippet, unit_type)}]}],
            "generationConfig": {"response_mime_type": "application/json", "temperature": 0.2}
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_endpoint, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                response_data = response.json()
                content_str = response_data['candidates'][0]['content']['parts'][0]['text']
                return json.loads(content_str)
            except requests.exceptions.RequestException as e:
                log_error(f"Warning: Network error on attempt {attempt + 1}: {e}")
                time.sleep(2 ** attempt)
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                log_error(f"Warning: Failed to parse Gemini response: {e}. Response: {response.text}")
                return None
        log_error(f"Error: Gemini API request failed after {max_retries} attempts.")
        return None

# --- Code Parsing and Documentation Logic ---
def calculate_hash(content):
    """Calculate a hash for the file content to detect changes."""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def parse_js_ts_with_helper(file_path):
    """Uses a Node.js helper script to parse JS/TS files."""
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'parse_js_ts.ts')
    ts_node_path = os.path.join(PROJECT_ROOT, 'backend', 'node_modules', '.bin', 'ts-node')
    try:
        # Note: Ensure ts-node is available in the environment
        result = subprocess.run(
            [ts_node_path, script_path, file_path],
            capture_output=True, text=True, check=True, cwd=os.path.join(PROJECT_ROOT, 'backend')
        )
        return json.loads(result.stdout)
    except (subprocess.CalledProcessError, json.JSONDecodeError, FileNotFoundError) as e:
        log_error(f"Error parsing with ts-node helper: {e}")
        # Fallback to regex if the helper fails
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return parse_with_regex(content)

def parse_file_for_units(file_path, content):
    """Uses AST to find functions and classes for Python files, otherwise uses regex."""
    if file_path.endswith('.py'):
        return parse_python_with_ast(content)
    elif file_path.endswith(('.js', '.ts', '.tsx')):
        return parse_js_ts_with_helper(file_path)
    else:
        return parse_with_regex(content)

import ast

def parse_python_with_ast(content):
    """Uses AST to find top-level functions and classes in Python code."""
    units = []
    try:
        tree = ast.parse(content)
        for node in tree.body: # Iterate over top-level nodes only
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                source_segment = ast.get_source_segment(content, node)
                if source_segment:
                     units.append({
                        "name": node.name,
                        "type": "function" if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)) else "class",
                        "content": source_segment
                    })

    except SyntaxError as e:
        log_error(f"SyntaxError parsing with AST: {e}")
        # Fallback to regex if AST parsing fails
        return parse_with_regex(content)
    
    if not units:
        units.append({"name": "file_overview", "type": "file", "content": content})

    return units

def parse_with_regex(content):
    """Uses regex to find functions and classes and returns them as units."""
    units = []
    # Regex to find class definitions, including decorators
    class_pattern = re.compile(r"((?:@\w+\s*\n)*)class\s+([\w\d_]+)\s*(\(|[\w\d_.,\s]*\))?:([\s\S]*?)(?=\nclass|\ndef|\Z)", re.DOTALL)
    # Regex to find function definitions, including decorators and async
    func_pattern = re.compile(r"((?:@\w+\s*\n)*)async\s+def\s+([\w\d_]+)\s*([\s\S]*?)\)[\s\S]*?:([\s\S]*?)(?=\n\w|\Z)|((?:@\w+\s*\n)*)def\s+([\w\d_]+)\s*([\s\S]*?)\)[\s\S]*?:([\s\S]*?)(?=\n\w|\Z)", re.DOTALL)
    
    # Find all functions and classes
    found_spans = []
    for match in class_pattern.finditer(content):
        found_spans.append(("class", match.group(2), match.start(), match.end()))
    for match in func_pattern.finditer(content):
        # The name is in group 2 for async, 6 for regular
        name = match.group(2) or match.group(6)
        found_spans.append(("function", name, match.start(), match.end()))

    # Sort by starting position to handle them in order
    found_spans.sort(key=lambda x: x[2])

    for unit_type, name, start, end in found_spans:
        units.append({"name": name, "type": unit_type, "content": content[start:end]})

    if not units:
        units.append({"name": "file_overview", "type": "file", "content": content})
        
    return units

def generate_markdown_from_doc(doc_json_path, doc_data):
    """Generates a Markdown file from the structured documentation data."""
    md_path = doc_json_path.replace('.doc.json', '.md')
    log_info(f"  -> Generating Markdown for: {md_path}")

    try:
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(f"# Documentation for `{doc_data['path']}`\n\n")
            f.write(f"**Generated:** `{doc_data['timestamp']}`\n\n")
            f.write("---\n\n")

            for unit in doc_data.get('units', []):
                f.write(f"## `{unit['unitName']}` ({unit['unitType'].capitalize()})\n\n")
                
                if unit.get('purpose'):
                    f.write(f"**Purpose:** {unit['purpose']}\n\n")

                if unit.get('humanReadableExplanation'):
                    f.write("### Detailed Explanation\n\n")
                    f.write(f"{unit['humanReadableExplanation']}\n\n")

                if unit.get('visualDiagram'):
                    f.write("### Visual Representation\n\n")
                    f.write("```mermaid\n")
                    f.write(unit['visualDiagram'])
                    f.write("\n```\n\n")

                if unit.get('inputs'):
                    f.write("### Inputs\n\n")
                    f.write("| Name | Type | Description |\n")
                    f.write("|------|------|-------------|\n")
                    for i in unit['inputs']:
                        f.write(f"| `{i.get('name', 'N/A')}` | `{i.get('type', 'N/A')}` | {i.get('description', 'N/A')} |\n")
                    f.write("\n")

                if unit.get('outputs'):
                    f.write("### Outputs\n\n")
                    outputs = unit['outputs']
                    f.write(f"- **Returns:** `{outputs.get('type', 'N/A')}` - {outputs.get('description', 'N/A')}\n")
                    if outputs.get('throws'):
                        f.write(f"- **Throws:** `{'`, `'.join(outputs['throws'])}`\n")
                    f.write("\n")

                if unit.get('dependencies'):
                    f.write("### Dependencies\n\n")
                    for dep in unit['dependencies']:
                        f.write(f"- **{dep.get('name', 'N/A')}** ({dep.get('type', 'N/A')})\n")
                    f.write("\n")

                f.write("---\n\n")
        
        log_info(f"  -> Saved Markdown to: {md_path}")
        return md_path
    except Exception as e:
        log_error(f"Error writing Markdown file for {doc_json_path}: {e}")
        return None

def is_excluded_dir(dir_name, excluded_dir_set):
    """Checks if a directory name should be excluded."""
    if dir_name in excluded_dir_set:
        return True
    # Handle suffix check, e.g., for '*.egg-info'
    if "*.egg-info" in excluded_dir_set and dir_name.endswith(".egg-info"):
        return True
    return False

def is_excluded_file(file_name, excluded_extension_set, excluded_filename_set):
    """Checks if a file should be excluded based on exact filename OR extension."""
    try:
        # Check 1: Exact filename match (case-insensitive)
        if file_name.lower() in excluded_filename_set:
            return True

        # Check 2: Extension match (case-insensitive)
        _, ext = os.path.splitext(file_name)
        # Proceed only if an actual extension exists (ignore '')
        if ext and ext.lower() in excluded_extension_set:
             return True

    except Exception as e:
        log_error(f"Warning: Could not process filename '{file_name}' for exclusion: {e}")
        return True

    return False

def collect_files_in_directory(directory_path, exclude_dirs=None, exclude_extensions=None, exclude_filenames=None):
    """Collect all code files in a directory, respecting exclusion rules."""
    if exclude_dirs is None:
        exclude_dirs = DEFAULT_EXCLUDED_DIRS
    if exclude_extensions is None:
        exclude_extensions = EXCLUDED_EXTENSIONS
    if exclude_filenames is None:
        exclude_filenames = set()

    found_files = []

    for dirpath, dirnames, filenames in os.walk(directory_path, topdown=True):
        # Filter out excluded directories
        dirnames[:] = [d for d in dirnames if not is_excluded_dir(d, exclude_dirs)]
        
        # Process files
        for filename in filenames:
            if not is_excluded_file(filename, exclude_extensions, exclude_filenames):
                file_path = os.path.join(dirpath, filename)
                found_files.append(file_path)
    
    return found_files

def process_file(file_path, llm_client):
    """Process a single file for documentation generation."""
    log_info(f"Processing: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception as e:
        log_error(f"Warning: Could not read file {file_path}: {e}")
        return None, None

    content_hash = calculate_hash(content)
    output_path = os.path.join(GENERATED_DOCS_BASE_DIR, file_path.replace(os.sep, '_') + ".doc.json")

    if os.path.exists(output_path):
        with open(output_path, 'r') as f:
            try:
                if json.load(f).get("contentHash") == content_hash:
                    log_info(f"  -> Skipping {file_path} (no changes detected).")
                    return None, None
            except json.JSONDecodeError:
                pass

    units_to_document = parse_file_for_units(file_path, content)
    documented_units = []
    for unit in units_to_document:
        log_info(f"  -> Documenting unit: {unit['name']} ({unit['type']})")
        enriched_data = llm_client.generate_documentation(file_path, content, unit['content'], unit['type'])
        if enriched_data:
            log_info(f"    -> Generating diagram for {unit['name']}")
            diagram = llm_client.generate_diagram(unit['content'], enriched_data)
            enriched_data['visualDiagram'] = diagram
            documented_units.append({"unitName": unit['name'], "unitType": unit['type'], **enriched_data})

    if not documented_units:
        log_error(f"  -> Failed to generate documentation for {file_path}.")
        return None, None

    timestamp = datetime.now().isoformat() + "Z"
    final_doc = {"path": file_path, "contentHash": content_hash, "timestamp": timestamp, "units": documented_units}

    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_doc, f, indent=2)
        log_info(f"  -> Saved doc to: {output_path}")
        
        # Generate Markdown from the final_doc data
        md_output_path = generate_markdown_from_doc(output_path, final_doc)
        
        return output_path, md_output_path
    except Exception as e:
        log_error(f"Error writing doc file for {file_path}: {e}")
        return None, None

def generate_documentation_for_file(file_path):
    """Generate documentation for a single file."""
    if not os.path.isfile(file_path):
        return {"error": f"File not found: {file_path}"}
    
    try:
        llm_client = LLMClient()
        json_path, md_path = process_file(file_path, llm_client)
        
        if json_path:
            return {
                "success": True,
                "file_path": file_path,
                "json_doc_path": json_path,
                "markdown_path": md_path
            }
        else:
            return {
                "success": False,
                "file_path": file_path,
                "error": "Failed to generate documentation"
            }
    except Exception as e:
        return {
            "success": False,
            "file_path": file_path,
            "error": str(e)
        }

def generate_documentation_for_directory(directory_path):
    """Generate documentation for all eligible files in a directory."""
    if not os.path.isdir(directory_path):
        return {"error": f"Directory not found: {directory_path}"}
    
    try:
        files = collect_files_in_directory(directory_path)
        results = []
        
        llm_client = LLMClient()
        for file_path in files:
            json_path, md_path = process_file(file_path, llm_client)
            if json_path:
                results.append({
                    "success": True,
                    "file_path": file_path,
                    "json_doc_path": json_path,
                    "markdown_path": md_path
                })
            else:
                results.append({
                    "success": False,
                    "file_path": file_path
                })
        
        return {
            "success": True,
            "directory_path": directory_path,
            "files_processed": len(files),
            "results": results
        }
    except Exception as e:
        return {
            "success": False,
            "directory_path": directory_path,
            "error": str(e)
        }

def main():
    parser = argparse.ArgumentParser(description="Generate documentation for a file or directory")
    parser.add_argument("path", help="File or directory path to generate documentation for")
    parser.add_argument("--type", choices=["file", "directory"], help="Specify if the path is a file or directory")
    
    args = parser.parse_args()
    
    # Determine if the path is a file or directory
    path_type = args.type
    if path_type is None:
        if os.path.isfile(args.path):
            path_type = "file"
        elif os.path.isdir(args.path):
            path_type = "directory"
        else:
            print(f"Error: {args.path} is neither a file nor a directory")
            return 1
    
    # Generate documentation based on the path type
    if path_type == "file":
        result = generate_documentation_for_file(args.path)
    else:
        result = generate_documentation_for_directory(args.path)
    
    # Print the result
    print(json.dumps(result, indent=2))
    return 0

if __name__ == "__main__":
    sys.exit(main())
