import os
import sys
import json
import subprocess
import re
import hashlib
import time
from datetime import datetime
from dotenv import load_dotenv
import requests

# --- Configuration & Constants ---

load_dotenv()
GENERATED_DOCS_BASE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'docs', 'generated', 'files')
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# --- Utility Functions ---
def log_error(message):
    print(message, file=sys.stderr)

def log_info(message):
    print(message, file=sys.stderr)

# --- LLM Client ---

class LLMClient:
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL_NAME", "gemini-1.5-flash")
        if not self.api_key or "YOUR_GEMINI_API_KEY_HERE" in self.api_key:
            raise ValueError("GEMINI_API_KEY is not configured in .env file.")
        self.api_endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent?key={self.api_key}"

    def _build_prompt(self, file_path, file_content, code_snippet, unit_type):
        diagram_type = "'graph TD'" if unit_type == "function" else "'classDiagram'"
        diagram_instruction = f"A Mermaid.js {diagram_type} that visually represents the logic. Be accurate and detailed."

        return f"""
You are an expert software engineer AI. Your task is to analyze the provided code snippet and generate documentation in a structured JSON format.

Analyze the following `{unit_type}` snippet from the file at `{file_path}`.

Full File Content (for context):
```
{file_content}
```

Code Snippet to Analyze:
```
{code_snippet}
```

Generate a single, valid JSON object that documents the snippet. The JSON object must adhere to the following schema. Do not include any explanatory text, markdown formatting, or anything else before or after the JSON object.

JSON Schema:
{{
  "purpose": "A brief, one-sentence explanation of what this code does from a business or feature perspective.",
  "humanReadableExplanation": "A detailed explanation of the code's logic, its role, and how it works. Aimed at a new developer.",
  "dependencies": [{{"type": "internal|external", "name": "function/class/library name"}}],
  "inputs": [{{"name": "param_name", "type": "param_type", "description": "Parameter purpose."}}],
  "outputs": {{"type": "return_type", "description": "What this function returns.", "throws": ["Potential error types."]}},
  "visualDiagram": "{diagram_instruction}"
}}
"""

    def _build_diagram_prompt(self, code_snippet, documentation_json):
        return f"""
You are a software documentation expert specializing in visual representation. Your task is to create a Mermaid.js diagram that accurately illustrates the provided code snippet and its purpose.

Analyze the code and its accompanying documentation to determine the most appropriate diagram type (e.g., flowchart for logic, sequence diagram for interactions, class diagram for structure).

Code Snippet:
```
{code_snippet}
```

JSON Documentation (for context):
```json
{documentation_json}
```

Based on the analysis, generate ONLY the Mermaid.js syntax for the most suitable diagram. Do not include the `mermaid` code block markers or any other explanatory text. The output should be the raw Mermaid syntax.
"""

    def generate_diagram(self, code_snippet, documentation_json, max_retries=3, timeout=60):
        headers = {"Content-Type": "application/json"}
        prompt = self._build_diagram_prompt(code_snippet, json.dumps(documentation_json, indent=2))
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {"temperature": 0.1}
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_endpoint, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                response_data = response.json()
                diagram_syntax = response_data['candidates'][0]['content']['parts'][0]['text']
                return diagram_syntax.strip()
            except requests.exceptions.RequestException as e:
                log_error(f"Warning: Network error on diagram generation attempt {attempt + 1}: {e}")
                time.sleep(2 ** attempt)
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                log_error(f"Warning: Failed to parse Gemini response for diagram: {e}. Response: {response.text}")
                return "graph TD\n  A[Error Generating Diagram]"
        log_error(f"Error: Gemini API request for diagram failed after {max_retries} attempts.")
        return "graph TD\n  A[API Request Failed]"

    def generate_documentation(self, file_path, file_content, code_snippet, unit_type, max_retries=3, timeout=90):
        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{"parts": [{"text": self._build_prompt(file_path, file_content, code_snippet, unit_type)}]}],
            "generationConfig": {"response_mime_type": "application/json", "temperature": 0.2}
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(self.api_endpoint, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                response_data = response.json()
                content_str = response_data['candidates'][0]['content']['parts'][0]['text']
                return json.loads(content_str)
            except requests.exceptions.RequestException as e:
                log_error(f"Warning: Network error on attempt {attempt + 1}: {e}")
                time.sleep(2 ** attempt)
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                log_error(f"Warning: Failed to parse Gemini response: {e}. Response: {response.text}")
                return None
        log_error(f"Error: Gemini API request failed after {max_retries} attempts.")
        return None

# --- Code Parsing and Documentation Logic ---

def get_staged_files():
    try:
        git_root = subprocess.run(['git', 'rev-parse', '--show-toplevel'], capture_output=True, text=True, check=True).stdout.strip()
        os.chdir(git_root)
        result = subprocess.run(['git', 'diff', '--cached', '--name-only', '--diff-filter=AM'], capture_output=True, text=True, check=True)
        return [f for f in result.stdout.strip().split('\n') if f]
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        log_error(f"Error getting staged files: {e}")
        return []

def calculate_hash(content):
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

import ast

def parse_js_ts_with_helper(file_path):
    """Uses a Node.js helper script to parse JS/TS files."""
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'parse_js_ts.ts')
    ts_node_path = os.path.join(PROJECT_ROOT, 'backend', 'node_modules', '.bin', 'ts-node')
    try:
        # Note: Ensure ts-node is available in the environment
        result = subprocess.run(
            [ts_node_path, script_path, file_path],
            capture_output=True, text=True, check=True, cwd=os.path.join(PROJECT_ROOT, 'backend')
        )
        return json.loads(result.stdout)
    except (subprocess.CalledProcessError, json.JSONDecodeError, FileNotFoundError) as e:
        log_error(f"Error parsing with ts-node helper: {e}")
        # Fallback to regex if the helper fails
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return parse_with_regex(content)

def parse_file_for_units(file_path, content):
    """Uses AST to find functions and classes for Python files, otherwise uses regex."""
    if file_path.endswith('.py'):
        return parse_python_with_ast(content)
    elif file_path.endswith(('.js', '.ts', '.tsx')):
        return parse_js_ts_with_helper(file_path)
    else:
        return parse_with_regex(content)

def parse_python_with_ast(content):
    """Uses AST to find top-level functions and classes in Python code."""
    units = []
    try:
        tree = ast.parse(content)
        for node in tree.body: # Iterate over top-level nodes only
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                source_segment = ast.get_source_segment(content, node)
                if source_segment:
                     units.append({
                        "name": node.name,
                        "type": "function" if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)) else "class",
                        "content": source_segment
                    })

    except SyntaxError as e:
        log_error(f"SyntaxError parsing with AST: {e}")
        # Fallback to regex if AST parsing fails
        return parse_with_regex(content)
    
    if not units:
        units.append({"name": "file_overview", "type": "file", "content": content})

    return units

def parse_with_regex(content):
    """Uses regex to find functions and classes and returns them as units."""
    units = []
    # Regex to find class definitions, including decorators
    class_pattern = re.compile(r"((?:@\w+\s*\n)*)class\s+([\w\d_]+)\s*(\(|[\w\d_.,\s]*\))?:([\s\S]*?)(?=\nclass|\ndef|\Z)", re.DOTALL)
    # Regex to find function definitions, including decorators and async
    func_pattern = re.compile(r"((?:@\w+\s*\n)*)async\s+def\s+([\w\d_]+)\s*([\s\S]*?)\)[\s\S]*?:([\s\S]*?)(?=\n\w|\Z)|((?:@\w+\s*\n)*)def\s+([\w\d_]+)\s*([\s\S]*?)\)[\s\S]*?:([\s\S]*?)(?=\n\w|\Z)", re.DOTALL)
    
    # A more robust solution would use an AST parser, but regex is a good start.
    # This simplified approach captures the whole block until the next function/class.
    
    # Find all functions and classes
    found_spans = []
    for match in class_pattern.finditer(content):
        found_spans.append(("class", match.group(2), match.start(), match.end()))
    for match in func_pattern.finditer(content):
        # The name is in group 2 for async, 6 for regular
        name = match.group(2) or match.group(6)
        found_spans.append(("function", name, match.start(), match.end()))

    # Sort by starting position to handle them in order
    found_spans.sort(key=lambda x: x[2])

    for unit_type, name, start, end in found_spans:
        units.append({"name": name, "type": unit_type, "content": content[start:end]})

    if not units:
        units.append({"name": "file_overview", "type": "file", "content": content})
        
    return units

def generate_markdown_from_doc(doc_json_path, doc_data):
    """Generates a Markdown file from the structured documentation data."""
    md_path = doc_json_path.replace('.doc.json', '.md')
    log_info(f"  -> Generating Markdown for: {md_path}")

    try:
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(f"# Documentation for `{doc_data['path']}`\n\n")
            f.write(f"**Commit:** `{doc_data['commit']}` | **Last Updated:** `{doc_data['timestamp']}`\n\n")
            f.write("---\n\n")

            for unit in doc_data.get('units', []):
                f.write(f"## `{unit['unitName']}` ({unit['unitType'].capitalize()})\n\n")
                
                if unit.get('purpose'):
                    f.write(f"**Purpose:** {unit['purpose']}\n\n")

                if unit.get('humanReadableExplanation'):
                    f.write("### Detailed Explanation\n\n")
                    f.write(f"{unit['humanReadableExplanation']}\n\n")

                if unit.get('visualDiagram'):
                    f.write("### Visual Representation\n\n")
                    f.write("```mermaid\n")
                    f.write(unit['visualDiagram'])
                    f.write("\n```\n\n")

                if unit.get('inputs'):
                    f.write("### Inputs\n\n")
                    f.write("| Name | Type | Description |\n")
                    f.write("|------|------|-------------|\n")
                    for i in unit['inputs']:
                        f.write(f"| `{i.get('name', 'N/A')}` | `{i.get('type', 'N/A')}` | {i.get('description', 'N/A')} |\n")
                    f.write("\n")

                if unit.get('outputs'):
                    f.write("### Outputs\n\n")
                    outputs = unit['outputs']
                    f.write(f"- **Returns:** `{outputs.get('type', 'N/A')}` - {outputs.get('description', 'N/A')}\n")
                    if outputs.get('throws'):
                        f.write(f"- **Throws:** `{'`, `'.join(outputs['throws'])}`\n")
                    f.write("\n")

                if unit.get('dependencies'):
                    f.write("### Dependencies\n\n")
                    for dep in unit['dependencies']:
                        f.write(f"- **{dep.get('name', 'N/A')}** ({dep.get('type', 'N/A')})\n")
                    f.write("\n")

                f.write("---\n\n")
        
        log_info(f"  -> Saved Markdown to: {md_path}")
        return md_path
    except Exception as e:
        log_error(f"Error writing Markdown file for {doc_json_path}: {e}")
        return None

def process_file(file_path, llm_client):
    log_info(f"Processing: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception as e:
        log_error(f"Warning: Could not read file {file_path}: {e}")
        return None, None

    content_hash = calculate_hash(content)
    output_path = os.path.join(GENERATED_DOCS_BASE_DIR, file_path.replace(os.sep, '_') + ".doc.json")

    if os.path.exists(output_path):
        with open(output_path, 'r') as f:
            try:
                if json.load(f).get("contentHash") == content_hash:
                    log_info("  -> Skipping (no changes detected).")
                    return None, None
            except json.JSONDecodeError: pass

    units_to_document = parse_file_for_units(file_path, content)
    documented_units = []
    for unit in units_to_document:
        log_info(f"  -> Documenting unit: {unit['name']} ({unit['type']})")
        enriched_data = llm_client.generate_documentation(file_path, content, unit['content'], unit['type'])
        if enriched_data:
            log_info(f"    -> Generating diagram for {unit['name']}")
            diagram = llm_client.generate_diagram(unit['content'], enriched_data)
            enriched_data['visualDiagram'] = diagram
            documented_units.append({"unitName": unit['name'], "unitType": unit['type'], **enriched_data})

    if not documented_units:
        log_error("  -> Failed to generate documentation.")
        return None, None

    commit_hash, timestamp = get_git_info()
    final_doc = {"path": file_path, "contentHash": content_hash, "commit": commit_hash, "timestamp": timestamp, "units": documented_units}

    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_doc, f, indent=2)
        log_info(f"  -> Saved doc to: {output_path}")
        
        # Generate Markdown from the final_doc data
        md_output_path = generate_markdown_from_doc(output_path, final_doc)
        
        return output_path, md_output_path
    except Exception as e:
        log_error(f"Error writing doc file for {file_path}: {e}")
        return None, None

def get_git_info():
    try:
        commit_hash = subprocess.run(['git', 'rev-parse', 'HEAD'], capture_output=True, text=True, check=True).stdout.strip()
        timestamp = subprocess.run(['git', 'log', '-1', '--format=%cd', '--date=iso-strict'], capture_output=True, text=True, check=True).stdout.strip()
        return commit_hash, timestamp
    except subprocess.CalledProcessError:
        return "unknown_commit", datetime.now().isoformat() + "Z"

def load_config():
    """Loads configuration from the JSON file."""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'config', 'doc_gen_config.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        log_error(f"Warning: Could not load config file. Using defaults. Error: {e}")
        return {
            "allowed_extensions": ['.py', '.js', '.ts', '.tsx', '.go', '.java', '.c', '.cpp', '.h', '.hpp', '.rs', '.swift', '.kt', '.kts'],
            "ignored_dirs": ['node_modules', 'venv', 'dist', 'build', 'generated']
        }

# --- Main Execution ---

def main():
    config = load_config()
    allowed_extensions = config.get("allowed_extensions", [])
    ignored_dirs = config.get("ignored_dirs", [])

    try:
        llm_client = LLMClient()
    except ValueError as e:
        log_error(f"Error: {e}")
        sys.exit(1)

    staged_files = get_staged_files()
    if not staged_files:
        log_info("No staged files to document. Exiting.")
        sys.exit(0)
        
    updated_doc_files = []
    for file_path in staged_files:
        # Ensure we are in the backend directory
        if os.path.abspath(file_path).startswith(os.path.join(PROJECT_ROOT, 'backend')):
            # Check if the file extension is in the allowed list and not in an ignored directory
            if any(file_path.endswith(ext) for ext in allowed_extensions) and not any(f'/{d}/' in os.path.normpath(file_path) for d in ignored_dirs):
                json_path, md_path = process_file(file_path, llm_client)
                if json_path:
                    updated_doc_files.append(json_path)
                if md_path:
                    updated_doc_files.append(md_path)

    if updated_doc_files:
        for path in updated_doc_files:
            print(path)
    else:
        log_info("No documentation was updated.")

if __name__ == "__main__":
    main()
