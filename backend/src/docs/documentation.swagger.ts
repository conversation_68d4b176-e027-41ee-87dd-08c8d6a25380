/**
 * @swagger
 * components:
 *   schemas:
 *     SearchResult:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique identifier for the documentation entry.
 *         unitName:
 *           type: string
 *           description: The name of the code unit (e.g., function name, class name).
 *         unitType:
 *           type: string
 *           description: The type of the code unit (e.g., function, class, interface).
 *         purpose:
 *           type: string
 *           description: The purpose or summary of the code unit.
 *         humanReadableExplanation:
 *           type: string
 *           description: A detailed, human-readable explanation of the code unit.
 *         visualDiagram:
 *           type: string
 *           description: A visual representation of the code unit's interactions (e.g., Mermaid.js diagram).
 *         similarity:
 *           type: number
 *           format: float
 *           description: The similarity score between the search query and the documentation entry.
 *       example:
 *         id: 1
 *         unitName: "searchDocumentation"
 *         unitType: "function"
 *         purpose: "Searches for documentation using a query string."
 *         humanReadableExplanation: "This function takes a user's query, generates a vector embedding, and searches a PostgreSQL database with pgvector for the most similar documentation entries."
 *         visualDiagram: "graph TD; A-->B;"
 *         similarity: 0.9876
 */
