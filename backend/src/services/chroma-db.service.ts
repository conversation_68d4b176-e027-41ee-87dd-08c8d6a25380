/**
 * ChromaDB Service
 * 
 * This service provides an interface to interact with ChromaDB for semantic search functionality.
 * It handles connecting to ChromaDB, managing collections, indexing documentation,
 * and performing semantic searches using vector embeddings.
 */

import { ChromaClient, Collection } from 'chromadb';
import * as dotenv from 'dotenv';

import { logger } from '../common/logger';

// Define the interface for the embedding function
interface EmbeddingFunctionType {
  embedDocuments(texts: string[]): Promise<number[][]>;
  embedQuery(text: string): Promise<number[]>;
  // Add generate method to match ChromaDB's EmbeddingFunction interface
  generate(texts: string[]): Promise<number[][]>;
}

// Load environment variables
dotenv.config();

// Configuration interface
interface ChromaConfig {
  host: string;
  port: number;
  collectionName: string;
  embeddingModel: string;
  apiKey?: string;
}

export class ChromaDBService {
  private client: ChromaClient;
  private collection: Collection | null = null;
  private embeddingFunction: EmbeddingFunctionType | undefined;
  private config: ChromaConfig;
  private isInitialized = false;

  constructor() {
    // Default configuration
    this.config = {
      host: process.env.CHROMA_HOST || 'localhost',
      port: parseInt(process.env.CHROMA_PORT || '8000', 10),
      collectionName: process.env.CHROMA_COLLECTION || 'documentation_embeddings',
      embeddingModel: process.env.EMBEDDING_MODEL || 'text-embedding-ada-002',
      apiKey: process.env.OPENAI_API_KEY
    };

    // Initialize ChromaDB client
    this.client = new ChromaClient({
      path: `http://${this.config.host}:${this.config.port}`
    });

    // Set up embedding function
    if (this.config.apiKey) {
      this.embeddingFunction = {
        // Implement embedDocuments function for batch processing
        embedDocuments: async (texts: string[]): Promise<number[][]> => {
          // In a real implementation, this would call OpenAI's API to get embeddings
          logger.info(`Getting embeddings for ${texts.length} documents using ${this.config.embeddingModel}`);
          
          // This is a placeholder. In production, this would call the OpenAI API
          try {
            // Here we'd use the OpenAI API to generate embeddings
            // For now, just return placeholders with dimension 3 for testing
            const placeholderEmbeddings = texts.map(() => [0.1, 0.2, 0.3]);
            return Promise.resolve(placeholderEmbeddings);
          } catch (error) {
            logger.error('Error getting embeddings:', error);
            throw error;
          }
        },
        // Implement embedQuery function for single queries
        embedQuery: async (text: string): Promise<number[]> => {
          // Similar to above but for a single query text
          logger.info(`Getting embedding for query: "${text}" using ${this.config.embeddingModel}`);
          
          // Placeholder implementation
          return Promise.resolve([0.1, 0.2, 0.3]);
        },
        // Implement generate method required by ChromaDB's interface
        generate: async (texts: string[]): Promise<number[][]> => {
          // In a real implementation, this would directly call OpenAI's API
          logger.info(
            `Generating embeddings for ${texts.length} documents using ${this.config.embeddingModel}`,
          );
          
          // This is a placeholder that matches embedDocuments implementation
          try {
            const placeholderEmbeddings = texts.map(() => [0.1, 0.2, 0.3]);
            return Promise.resolve(placeholderEmbeddings);
          } catch (error) {
            logger.error('Error generating embeddings:', error);
            throw error;
          }
        },
      };
    } else {
      logger.warn('No OpenAI API key provided. Will use default embedding function.');
      this.embeddingFunction = undefined;
    }
  }

  /**
   * Initialize the ChromaDB connection and collection
   */
  public async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return;
      }

      logger.info(`Initializing ChromaDB connection to ${this.config.host}:${this.config.port}`);
      
      // Try to get existing collection or create a new one
      try {
        this.collection = await this.client.getCollection({
          name: this.config.collectionName,
          embeddingFunction: this.embeddingFunction
        });
        logger.info(`Connected to existing collection: ${this.config.collectionName}`);
      } catch (err) {
        logger.info(`Creating new collection: ${this.config.collectionName}`);
        this.collection = await this.client.createCollection({
          name: this.config.collectionName,
          embeddingFunction: this.embeddingFunction
        });
      }

      this.isInitialized = true;
      logger.info('ChromaDB service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ChromaDB service:', error);
      throw new Error(`ChromaDB initialization failed: ${error}`);
    }
  }

  /**
   * Add a document to the vector database
   * @param id Unique document identifier
   * @param content Text content for embedding
   * @param metadata Additional metadata about the document
   */
  public async addDocument(id: string, content: string, metadata: Record<string, any>): Promise<void> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      logger.debug(`Adding document to ChromaDB: ${id}`);
      
      await this.collection!.add({
        ids: [id],
        documents: [content],
        metadatas: [metadata]
      });
      
      logger.debug(`Document added successfully: ${id}`);
    } catch (error) {
      logger.error(`Failed to add document ${id}:`, error);
      throw new Error(`Failed to add document to ChromaDB: ${error}`);
    }
  }

  /**
   * Add multiple documents to the vector database in batch
   * @param ids Array of unique document identifiers
   * @param contents Array of text contents for embedding
   * @param metadatas Array of metadata objects
   */
  public async addDocuments(
    ids: string[],
    contents: string[],
    metadatas: Record<string, any>[]
  ): Promise<void> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      if (ids.length !== contents.length || ids.length !== metadatas.length) {
        throw new Error('Arrays of ids, contents, and metadatas must have the same length');
      }

      logger.debug(`Adding ${ids.length} documents to ChromaDB in batch`);
      
      await this.collection!.add({
        ids: ids,
        documents: contents,
        metadatas: metadatas
      });
      
      logger.debug(`${ids.length} documents added successfully`);
    } catch (error) {
      logger.error('Failed to add documents in batch:', error);
      throw new Error(`Failed to add documents to ChromaDB: ${error}`);
    }
  }

  /**
   * Perform a semantic search using natural language query
   * @param query Natural language search query
   * @param limit Maximum number of results to return
   * @param filters Optional metadata filters
   * @returns Array of search results with document content and metadata
   */
  public async semanticSearch(
    query: string,
    limit: number = 10,
    filters: Record<string, any> = {}
  ): Promise<any> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      logger.debug(`Performing semantic search: "${query}" (limit: ${limit})`);
      
      const results = await this.collection!.query({
        queryTexts: [query],
        nResults: limit,
        ...(Object.keys(filters).length > 0 ? { where: filters } : {})
      });

      // Transform results to a more usable format
      const formattedResults = results.ids[0].map((id, index) => {
        return {
          id,
          content: results.documents?.[0]?.[index] || '',
          metadata: results.metadatas?.[0]?.[index] || {},
          distance: results.distances?.[0]?.[index] || 0
        };
      });
      
      logger.debug(`Semantic search returned ${formattedResults.length} results`);
      return formattedResults;
    } catch (error) {
      logger.error('Semantic search failed:', error);
      throw new Error(`Semantic search failed: ${error}`);
    }
  }

  /**
   * Delete documents from the collection
   * @param ids Array of document IDs to delete
   */
  public async deleteDocuments(ids: string[]): Promise<void> {
    try {
      if (!this.isInitialized || !this.collection) {
        await this.initialize();
      }

      logger.debug(`Deleting ${ids.length} documents from ChromaDB`);
      await this.collection!.delete({ ids });
      logger.debug('Documents deleted successfully');
    } catch (error) {
      logger.error('Failed to delete documents:', error);
      throw new Error(`Failed to delete documents from ChromaDB: ${error}`);
    }
  }

  /**
   * Check if the ChromaDB service is available
   * @returns Boolean indicating if service is healthy
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // Simple health check by getting collection count
      const collections = await this.client.listCollections();
      logger.debug(`ChromaDB health check: Found ${collections.length} collections`);
      return true;
    } catch (error) {
      logger.error('ChromaDB health check failed:', error);
      return false;
    }
  }
}

// Create singleton instance
export const chromaDBService = new ChromaDBService();
