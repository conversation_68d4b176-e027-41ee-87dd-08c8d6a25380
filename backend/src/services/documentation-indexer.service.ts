/**
 * Documentation Indexer Service
 * 
 * This service is responsible for extracting content from documentation files,
 * preparing it for embeddings, and storing it in ChromaDB for semantic search.
 * It integrates with the existing documentation generation pipeline.
 */

import * as fs from 'fs';
import * as path from 'path';
import glob from 'glob';

import { logger } from '../common/logger';
import { chromaDBService } from './chroma-db.service';

interface DocumentationMetadata {
  filePath: string;
  fileName: string;
  sourceFile?: string;
  commit?: string;
  timestamp?: string;
  unit?: string;
  type?: string;
}

export class DocumentationIndexerService {
  /**
   * Process and index a single documentation file
   * @param filePath Path to the documentation file (.doc.json)
   */
  public async indexDocumentationFile(filePath: string): Promise<void> {
    try {
      logger.info(`Indexing documentation file: ${filePath}`);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`Documentation file does not exist: ${filePath}`);
      }
      
      // Parse the documentation file
      const content = fs.readFileSync(filePath, 'utf8');
      let parsedContent: any;
      
      try {
        parsedContent = JSON.parse(content);
      } catch (err) {
        logger.error(`Failed to parse documentation file as JSON: ${filePath}`);
        throw err;
      }
      
      // Extract text for embedding
      const textForEmbedding = this.extractTextForEmbedding(parsedContent);
      
      // Skip if there's not enough meaningful content
      if (!textForEmbedding || textForEmbedding.trim().length < 10) {
        logger.warn(`Skipping document with insufficient content: ${filePath}`);
        return;
      }
      
      // Prepare metadata
      const metadata: DocumentationMetadata = {
        filePath: filePath,
        fileName: path.basename(filePath),
        sourceFile: parsedContent.path || '',
        commit: parsedContent.commit || '',
        timestamp: parsedContent.timestamp || new Date().toISOString(),
        unit: parsedContent.unit || '',
        type: parsedContent.type || 'documentation'
      };
      
      // Generate a unique ID for the document
      const id = this.generateDocumentId(parsedContent, filePath);
      
      // Add to ChromaDB
      await chromaDBService.addDocument(id, textForEmbedding, metadata);
      logger.info(`Successfully indexed document: ${id}`);
    } catch (error) {
      logger.error(`Failed to index documentation file ${filePath}:`, error);
      throw error;
    }
  }
  
  /**
   * Process and index all documentation files in a project
   * @param projectPath Root path of the project
   * @param force Whether to force reindexing of all documents
   */
  public async indexProjectDocumentation(projectPath: string, force: boolean = false): Promise<void> {
    try {
      logger.info(`Indexing all documentation files for project: ${projectPath}`);
      
      // Define the docs directory pattern
      const docsPattern = path.join(projectPath, '**', 'docs', 'generated', '**', '*.doc.json');
      const docsPattern2 = path.join(projectPath, 'docs', 'generated', '**', '*.doc.json');
      
      // Find all documentation files
      const docFiles = [
        ...glob.sync(docsPattern, { ignore: '**/node_modules/**' }),
        ...glob.sync(docsPattern2, { ignore: '**/node_modules/**' })
      ];
      
      logger.info(`Found ${docFiles.length} documentation files to index`);
      
      // Process files in batches to prevent memory issues
      const batchSize = 10;
      for (let i = 0; i < docFiles.length; i += batchSize) {
        const batch = docFiles.slice(i, i + batchSize);
        await Promise.all(batch.map(file => this.indexDocumentationFile(file)));
        logger.debug(`Indexed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(docFiles.length / batchSize)}`);
      }
      
      logger.info(`Successfully indexed all ${docFiles.length} documentation files`);
    } catch (error) {
      logger.error(`Failed to index project documentation:`, error);
      throw error;
    }
  }
  
  /**
   * Extract the text that will be used for embedding
   * @param docContent The parsed documentation content
   * @returns Text prepared for embedding
   */
  private extractTextForEmbedding(docContent: any): string {
    // Gather all relevant text fields for embedding
    const textParts: string[] = [];
    
    // Include the purpose/summary if available
    if (docContent.purpose) {
      textParts.push(`Purpose: ${docContent.purpose}`);
    } else if (docContent.summary) {
      textParts.push(`Summary: ${docContent.summary}`);
    }
    
    // Include description or details
    if (docContent.description) {
      textParts.push(`Description: ${docContent.description}`);
    }
    
    // Include developer intent information
    if (docContent.intent) {
      textParts.push(`Intent: ${docContent.intent}`);
    }
    
    // Include inputs/parameters information
    if (docContent.inputs && Array.isArray(docContent.inputs)) {
      const inputsText = docContent.inputs
        .map((input: any) => `${input.name}: ${input.type} - ${input.description || ''}`)
        .join('\n');
      textParts.push(`Inputs:\n${inputsText}`);
    }
    
    // Include outputs information
    if (docContent.outputs) {
      if (typeof docContent.outputs === 'object') {
        textParts.push(`Output: ${JSON.stringify(docContent.outputs)}`);
      } else {
        textParts.push(`Output: ${docContent.outputs}`);
      }
    }
    
    // Include dependencies
    if (docContent.dependencies && Array.isArray(docContent.dependencies)) {
      textParts.push(`Dependencies: ${docContent.dependencies.join(', ')}`);
    }
    
    // Include any code examples
    if (docContent.examples) {
      textParts.push(`Examples: ${docContent.examples}`);
    }
    
    // Combine all text parts with spacing
    return textParts.join('\n\n');
  }
  
  /**
   * Generate a unique ID for the document
   * @param docContent The parsed documentation content
   * @param filePath The file path as fallback
   * @returns Unique document ID
   */
  private generateDocumentId(docContent: any, filePath: string): string {
    if (docContent.path && docContent.unit) {
      // Use source path and unit name if available
      return `doc_${docContent.path.replace(/[\/\\. ]/g, '_')}_${docContent.unit}`;
    }
    
    // Fallback to using the file path
    return `doc_${filePath.replace(/[\/\\. ]/g, '_')}`;
  }
  
  /**
   * Clear all indexed documentation for a project
   * @param projectPath Root path of the project
   */
  public async clearProjectDocumentation(projectPath: string): Promise<void> {
    try {
      // This would require implementing a way to filter by project path in ChromaDB
      // For now, we'll log a warning that this functionality isn't implemented
      logger.warn(`Clearing project documentation is not yet implemented`);
      // In a complete implementation, we would:
      // 1. Query all documents with metadata.filePath starting with projectPath
      // 2. Get their IDs
      // 3. Delete them from ChromaDB
    } catch (error) {
      logger.error(`Failed to clear project documentation:`, error);
      throw error;
    }
  }
}

// Create singleton instance
export const documentationIndexerService = new DocumentationIndexerService();
