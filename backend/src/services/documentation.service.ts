import { spawn } from 'child_process';
import * as path from 'path';

export async function generateDocumentation(
  projectPath: string,
  filePath?: string
): Promise<void> {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(__dirname, '..', '..', 'scripts', 'generate_docs.py');
    const args: string[] = [];

    if (filePath) {
      args.push('--file', filePath);
    } else {
      args.push('--dir', projectPath);
    }

    console.log(`Running documentation generation script for: ${filePath || projectPath}`);
    console.log(`Executing: python3 ${scriptPath} ${args.join(' ')}`);

    const scriptsDir = path.dirname(scriptPath);

    const pythonProcess = spawn('poetry', ['run', 'python3', scriptPath, ...args], { cwd: scriptsDir });

    pythonProcess.stdout.on('data', (data) => {
      console.log(`[DocGen <PERSON>ript]: ${data}`);
    });

    pythonProcess.stderr.on('data', (data) => {
      console.error(`[DocGen Script Error]: ${data}`);
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Documentation generation script finished successfully.');
        resolve();
      } else {
        console.error(`Documentation generation script exited with code ${code}`);
        reject(new Error(`Documentation generation failed with exit code ${code}`));
      }
    });

    pythonProcess.on('error', (err) => {
        console.error('Failed to start documentation generation script:', err);
        reject(err);
    });
  });
}
