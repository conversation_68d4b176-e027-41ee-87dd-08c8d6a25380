import { simpleGit } from 'simple-git';
import * as fs from 'fs/promises';
import * as path from 'path';

async function getChangedFiles(projectPath: string): Promise<string[]> {
  const git = simpleGit(projectPath);
  const diff = await git.diff(['--name-only', 'HEAD']);
  return diff.split('\n').filter(Boolean);
}

async function generateDocForFile(filePath: string): Promise<string> {
  const content = await fs.readFile(filePath, 'utf-8');
  // This is a placeholder. In a real implementation, we would use an AI model
  // to generate documentation based on the file content.
  return `/**
 * @description This is an auto-generated documentation for ${path.basename(
   filePath
 )}.
 * @lastmodified ${new Date().toISOString()}
 */
${content}`;
}

export async function generateDocumentation(projectPath: string): Promise<void> {
  console.log(`Generating documentation for project at ${projectPath}`);
  const changedFiles = await getChangedFiles(projectPath);
  console.log('Changed files:', changedFiles);

  for (const file of changedFiles) {
    const fullPath = path.join(projectPath, file);
    const newContent = await generateDocForFile(fullPath);
    await fs.writeFile(fullPath, newContent);
    console.log(`Documentation generated for ${file}`);
  }
}
