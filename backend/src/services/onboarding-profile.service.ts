/**
 * Onboarding Profile Service
 * 
 * Extracts user profile information from conversation messages
 * and updates the local .kapi/user_profile.yaml file
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { logger } from '../common/logger';
import aiService from './ai/index';

export interface UserProfile {
  id?: string;
  created_at?: string;
  updated_at?: string;
  
  // Discovery layer
  discovery?: {
    source?: string;
    initial_intent?: 'learn_ai' | 'build_project' | 'explore' | 'team_adoption';
    experience_self_reported?: 'beginner' | 'intermediate' | 'advanced';
    motivation?: string;
  };
  
  // Interaction preferences
  interaction?: {
    prefers_voice?: boolean;
    communication_style?: string;
  };
  
  // Technical background
  technical?: {
    languages?: string[];
    frameworks?: string[];
    experience_level?: string;
    interests?: string[];
  };
  
  // Personal
  personal?: {
    name?: string;
    goals?: string[];
    learning_style?: string;
    timeline?: string;
  };
  
  // Derived insights
  insights?: {
    confidence_level?: 'low' | 'medium' | 'high';
    learning_pace?: 'slow' | 'moderate' | 'fast';
    complexity_preference?: 'simple' | 'moderate' | 'advanced';
    help_seeking_pattern?: string;
  };
}

export interface ProfileExtractionResult {
  profile: UserProfile;
  summary: {
    motivation: string;
    experience: string;
    next_questions: string[];
  };
  confidence: number;
}

class OnboardingProfileService {
  
  /**
   * Extract user profile from conversation messages
   */
  async extractProfile(messages: Array<{role: string, content: string}>): Promise<ProfileExtractionResult> {
    try {
      logger.info(`🎯 [PROFILE-SERVICE] Starting extraction with ${messages.length} messages`);
      
      const conversationText = messages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n\n');
        
      logger.info(`🎯 [PROFILE-SERVICE] Conversation text length: ${conversationText.length}`);

      const extractionPrompt = `You are an expert user profiler. Analyze this onboarding conversation and extract a comprehensive user profile.

CONVERSATION:
${conversationText}

CRITICAL: You must respond with ONLY valid JSON. No explanation, no markdown, no code blocks. Just the JSON object.

EXTRACT THE FOLLOWING in valid JSON format:

{
  "profile": {
    "discovery": {
      "initial_intent": "learn_ai" | "build_project" | "explore" | "team_adoption",
      "experience_self_reported": "beginner" | "intermediate" | "advanced",
      "motivation": "brief description of what drives them"
    },
    "technical": {
      "languages": ["array of mentioned programming languages"],
      "frameworks": ["array of mentioned frameworks/tools"],
      "experience_level": "description of their technical experience",
      "interests": ["array of technical interests mentioned"]
    },
    "personal": {
      "name": "extracted name if mentioned",
      "role": "developer | pm | designer | tester | architect | student | founder | other",
      "goals": ["array of stated goals or aspirations"],
      "learning_style": "description of how they prefer to learn"
    },
    "insights": {
      "confidence_level": "low" | "medium" | "high",
      "learning_pace": "slow" | "moderate" | "fast", 
      "complexity_preference": "simple" | "moderate" | "advanced"
    }
  },
  "summary": {
    "motivation": "1-2 sentence summary of their main motivation",
    "experience": "1-2 sentence summary of their experience level",
    "next_questions": ["3-5 intelligent follow-up questions to ask"]
  },
  "confidence": 0.0-1.0
}

RULES:
- Only include information explicitly mentioned or strongly implied
- Use null for unknown fields
- Be conservative with confidence scoring
- Focus on actionable insights
- Make next_questions specific and personalized

RESPONSE FORMAT: Return ONLY the JSON object. Start with { and end with }. No other text.`;

      logger.info(`🎯 [PROFILE-SERVICE] Sending to AI service...`);
      
      const result = await aiService.generateText({
        prompt: extractionPrompt,
        model: 'gpt-4.1',
        maxTokens: 1500,
        temperature: 0.3,
      });

      logger.info(`🎯 [PROFILE-SERVICE] AI response received, length: ${result.content.length}`);
      logger.info(`🎯 [PROFILE-SERVICE] AI response preview: ${result.content.substring(0, 200)}...`);

      // Clean and extract JSON from AI response (may be wrapped in markdown or have explanatory text)
      let cleanedJson = result.content.trim();
      
      // First, look for JSON block markers
      const jsonBlockMatch = cleanedJson.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonBlockMatch) {
        cleanedJson = jsonBlockMatch[1].trim();
      } else {
        // Look for any ```-wrapped content
        const codeBlockMatch = cleanedJson.match(/```\s*([\s\S]*?)\s*```/);
        if (codeBlockMatch) {
          cleanedJson = codeBlockMatch[1].trim();
        } else {
          // Look for JSON object starting with { and ending with }
          const jsonMatch = cleanedJson.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            cleanedJson = jsonMatch[0].trim();
          }
        }
      }
      
      logger.info(`🎯 [PROFILE-SERVICE] Cleaned JSON for parsing: ${cleanedJson.substring(0, 200)}...`);

      let extracted: ProfileExtractionResult;
      try {
        extracted = JSON.parse(cleanedJson) as ProfileExtractionResult;
      } catch (parseError) {
        logger.warn(`🎯 [PROFILE-SERVICE] JSON parsing failed, creating minimal profile. Parse error:`, parseError);
        logger.warn(`🎯 [PROFILE-SERVICE] Failed to parse content: ${cleanedJson}`);
        
        // Create a minimal profile as fallback
        extracted = {
          profile: {
            discovery: {
              initial_intent: null,
              experience_self_reported: null,
              motivation: null
            },
            technical: {
              languages: [],
              frameworks: [],
              experience_level: null,
              interests: []
            },
            personal: {
              name: null,
              role: null,
              goals: [],
              learning_style: null
            },
            insights: {
              confidence_level: 'low',
              learning_pace: 'moderate',
              complexity_preference: 'simple'
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          summary: {
            motivation: "Unable to extract - conversation too brief",
            experience: "Unknown - needs more conversation",
            next_questions: [
              "What's your name?",
              "What's your role?", 
              "What brings you to KAPI?"
            ]
          },
          confidence: 0.1
        };
      }
      
      // Add metadata
      if (extracted.profile) {
        extracted.profile.updated_at = new Date().toISOString();
        if (!extracted.profile.created_at) {
          extracted.profile.created_at = new Date().toISOString();
        }
      }

      logger.info('🎯 [PROFILE-SERVICE] Successfully extracted user profile with confidence:', extracted.confidence);
      return extracted;

    } catch (error) {
      logger.error('Error extracting profile:', error);
      throw error;
    }
  }

  /**
   * Save profile to .kapi/user_profile.yaml in user's home directory
   */
  async saveProfile(profile: UserProfile, projectDir?: string): Promise<void> {
    try {
      // Use user's home directory if no project directory specified
      let baseDir = projectDir;
      if (!baseDir) {
        const os = require('os');
        baseDir = os.homedir();
        logger.info(`🎯 [PROFILE-SERVICE] Using user home directory: ${baseDir}`);
      }
      
      const kapiDir = path.join(baseDir, '.kapi');
      const profilePath = path.join(kapiDir, 'user_profile.yaml');
      
      logger.info(`🎯 [PROFILE-SERVICE] Attempting to save profile to: ${profilePath}`);

      // Ensure .kapi directory exists
      await fs.mkdir(kapiDir, { recursive: true });

      // Load existing profile if it exists
      let existingProfile: UserProfile = {};
      try {
        const existingContent = await fs.readFile(profilePath, 'utf8');
        existingProfile = yaml.load(existingContent) as UserProfile;
      } catch (error) {
        // File doesn't exist yet, that's fine
      }

      // Merge with existing profile (new data takes precedence)
      const mergedProfile = this.mergeProfiles(existingProfile, profile);
      
      logger.info(`🎯 [PROFILE-SERVICE] Merged profile keys:`, Object.keys(mergedProfile));

      // Save to YAML
      const yamlContent = yaml.dump(mergedProfile, {
        indent: 2,
        lineWidth: 120,
        noRefs: true
      });

      logger.info(`🎯 [PROFILE-SERVICE] Generated YAML content length: ${yamlContent.length}`);

      await fs.writeFile(profilePath, yamlContent, 'utf8');
      logger.info(`🎯 [PROFILE-SERVICE] Profile successfully saved to ${profilePath}`);

    } catch (error) {
      logger.error('Error saving profile:', error);
      throw error;
    }
  }

  /**
   * Load existing profile from .kapi/user_profile.yaml in user's home directory
   */
  async loadProfile(projectDir?: string): Promise<UserProfile | null> {
    try {
      // Use user's home directory if no project directory specified
      let baseDir = projectDir;
      if (!baseDir) {
        const os = require('os');
        baseDir = os.homedir();
      }
      
      const profilePath = path.join(baseDir, '.kapi', 'user_profile.yaml');

      const content = await fs.readFile(profilePath, 'utf8');
      const profile = yaml.load(content) as UserProfile;
      
      logger.info('🎯 [PROFILE-SERVICE] Profile loaded from disk');
      return profile;

    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        logger.info('🎯 [PROFILE-SERVICE] No existing profile found');
        return null;
      }
      logger.error('Error loading profile:', error);
      throw error;
    }
  }

  /**
   * Merge two profiles, giving precedence to new data
   */
  private mergeProfiles(existing: UserProfile, updates: UserProfile): UserProfile {
    const merged: UserProfile = { ...existing };

    // Merge each section
    if (updates.discovery) {
      merged.discovery = { ...existing.discovery, ...updates.discovery };
    }
    if (updates.interaction) {
      merged.interaction = { ...existing.interaction, ...updates.interaction };
    }
    if (updates.technical) {
      merged.technical = { 
        ...existing.technical, 
        ...updates.technical,
        // Merge arrays
        languages: [...(existing.technical?.languages || []), ...(updates.technical?.languages || [])].filter((v, i, a) => a.indexOf(v) === i),
        frameworks: [...(existing.technical?.frameworks || []), ...(updates.technical?.frameworks || [])].filter((v, i, a) => a.indexOf(v) === i),
        interests: [...(existing.technical?.interests || []), ...(updates.technical?.interests || [])].filter((v, i, a) => a.indexOf(v) === i)
      };
    }
    if (updates.personal) {
      merged.personal = { 
        ...existing.personal, 
        ...updates.personal,
        // Merge arrays
        goals: [...(existing.personal?.goals || []), ...(updates.personal?.goals || [])].filter((v, i, a) => a.indexOf(v) === i)
      };
    }
    if (updates.insights) {
      merged.insights = { ...existing.insights, ...updates.insights };
    }

    // Update metadata
    merged.updated_at = updates.updated_at || new Date().toISOString();
    if (!merged.created_at) {
      merged.created_at = updates.created_at || new Date().toISOString();
    }

    return merged;
  }

  /**
   * Process onboarding conversation and update profile
   */
  async processOnboardingConversation(messages: Array<{role: string, content: string}>, projectDir?: string): Promise<ProfileExtractionResult> {
    try {
      // Extract profile from conversation
      const extracted = await this.extractProfile(messages);

      // Save to disk
      await this.saveProfile(extracted.profile, projectDir);

      logger.info('🎯 [PROFILE-SERVICE] Onboarding conversation processed and profile updated');
      return extracted;

    } catch (error) {
      logger.error('Error processing onboarding conversation:', error);
      throw error;
    }
  }
}

export const onboardingProfileService = new OnboardingProfileService();
export default onboardingProfileService;