import { Request, Response } from 'express';
import { clerkService } from '../../../services/clerk.service';
import { logger } from '../../../common/logger';

/**
 * Controller for auth-related endpoints
 */
export class AuthController {
  /**
   * Get the current user's profile
   */
  async getCurrentUser(req: Request, res: Response) {
    try {
      // Cast the request to access userId
      const userId = (req as any).userId;

      // Validate that userId is present and valid
      if (!userId || (typeof userId !== 'number' && typeof userId !== 'string')) {
        logger.error('Auth Controller: Missing or invalid userId in request', { userId, type: typeof userId });
        return res.status(401).json({
          success: false,
          message: 'Invalid authentication - missing user ID'
        });
      }

      // Check if this is a development bypass (userId = 1)
      if (process.env.NODE_ENV === 'development' && (userId === 1 || userId === '1')) {
        logger.info('Auth Controller: Using development bypass user data');
        
        // Return mock user data for development
        res.status(200).json({
          success: true,
          data: {
            id: 1,
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'User',
            username: 'admin',
            role: 'admin',
            email_verified: true,
            imageUrl: null,
            publicMetadata: {},
          },
        });
        return;
      }

      // Get the user from Clerk for real authentication
      const user = await clerkService.getUserById(userId);

      res.status(200).json({
        success: true,
        data: {
          id: user.id,
          email: user.email_addresses?.[0]?.email_address,
          firstName: user.first_name,
          lastName: user.last_name,
          username: user.username,
          imageUrl: user.image_url,
          publicMetadata: user.public_metadata,
        },
      });
    } catch (error: any) {
      logger.error('Error fetching current user:', error);
      res.status(500).json({
        success: false,
        message: 'Error fetching user profile',
        details: error?.message || 'Unknown error',
      });
    }
  }
}

export const authController = new AuthController();
