import { Router } from 'express';
import { generateDocumentation } from '../services/documentation.service';

const router = Router();

/**
 * @swagger
 * /documentation/generate:
 *   post:
 *     summary: Generate documentation for a project
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: The absolute path to the project directory.
 *     responses:
 *       200:
 *         description: Documentation generation started
 *       400:
 *         description: projectPath is required
 *       500:
 *         description: Error generating documentation
 */
router.post('/generate', async (req, res) => {
  try {
    const { projectPath } = req.body;
    if (!projectPath) {
      return res.status(400).send('projectPath is required');
    }
    await generateDocumentation(projectPath);
    res.status(200).send('Documentation generation started');
  } catch (error) {
    console.error('Error generating documentation:', error);
    res.status(500).send('Error generating documentation');
  }
});

export default router;
