import { Router } from 'express';
import { generateDocumentation } from '../services/documentation.service';

const router = Router();

/**
 * @swagger
 * /documentation/generate:
 *   post:
 *     summary: Generate documentation for a project
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: The absolute path to the project directory.
 *     responses:
 *       200:
 *         description: Documentation generation started
 *       400:
 *         description: projectPath is required
 *       500:
 *         description: Error generating documentation
 */
router.post('/generate', async (req, res) => {
  try {
    const { projectPath, filePath } = req.body; // filePath is optional
    if (!projectPath) {
      return res.status(400).send('projectPath is required');
    }
    // We run this in the background and immediately return a response
    generateDocumentation(projectPath, filePath).catch(error => {
        console.error('Error during background documentation generation:', error);
    });
    res.status(202).send('Documentation generation started in the background.');
  } catch (error) {
    console.error('Error starting documentation generation:', error);
    res.status(500).send('Error starting documentation generation');
  }
});

export default router;
