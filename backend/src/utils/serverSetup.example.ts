// This file demonstrates how to set up Express v5 with proper async error handling
// Express v5 has better native async/await support
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express, { Application } from 'express';
import helmet from 'helmet';

import { env } from '@/config/env.validation';
import { logger, loggerStream } from '@/config/logger';
import {
  errorHandler,
  notFoundHandler,
  handleUnhandledRejections,
  handleUncaughtExceptions,
} from '@/middleware/errorHandler';
import { validate, body } from '@/middleware/validation';
import { asyncHandler, ApiErrors } from '@/utils/asyncHandler';

// Handle process errors
handleUnhandledRejections();
handleUncaughtExceptions();

export function createApp(): Application {
  const app = express();

  // Trust proxy
  app.set('trust proxy', true);

  // Security middleware
  app.use(helmet());
  app.use(
    cors({
      origin:
        env.NODE_ENV === 'production' ? ['https://kapihq.com', 'https://modernaipro.com'] : true,
      credentials: true,
    }),
  );

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());

  // Compression
  app.use(compression());

  // Request logging
  app.use((req, res, next) => {
    logger.info({
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('user-agent'),
    });
    next();
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: env.NODE_ENV,
    });
  });

  // Example route with async handler
  app.get(
    '/api/example',
    asyncHandler(async (req, res) => {
      // This will automatically catch any errors and pass them to the error handler
      const data = await someAsyncOperation();
      res.json({ data });
    }),
  );

  // Example route that throws an error
  app.get(
    '/api/error-example',
    asyncHandler(async (req, res) => {
      // This error will be caught and handled properly
      throw ApiErrors.BadRequest('This is an example error');
    }),
  );

  // Example route with validation
  app.post(
    '/api/users',
    validate([
      body('email').isEmail().normalizeEmail(),
      body('name').trim().notEmpty().isLength({ min: 2, max: 50 }),
    ]),
    asyncHandler(async (req, res) => {
      // Validation errors are automatically handled
      const { email, name } = req.body;
      const user = await createUser({ email, name });
      res.status(201).json({ user });
    }),
  );

  // Mount your routes here
  // app.use('/api/v1', apiRoutes);

  // 404 handler (must be after all routes)
  app.use(notFoundHandler);

  // Global error handler (must be last)
  app.use(errorHandler);

  return app;
}

// Example async operation
async function someAsyncOperation() {
  return { message: 'Success' };
}

// Example user creation
async function createUser(data: { email: string; name: string }) {
  // Your user creation logic here
  return { id: 1, ...data };
}

// Import validation utilities
