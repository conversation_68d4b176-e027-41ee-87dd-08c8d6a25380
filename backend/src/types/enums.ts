// Temporary enum definitions until Prisma client is generated
export enum AgentStatus {
  initializing = 'initializing',
  running = 'running',
  paused = 'paused',
  completed = 'completed',
  failed = 'failed',
  waiting = 'waiting',
  cancelled = 'cancelled',
}

export enum LastActivityType {
  thinking = 'thinking',
  command = 'command',
  file_edit = 'file_edit',
  complete = 'complete',
  error = 'error',
  user_input = 'user_input',
}
