// Use declaration merging to add custom properties to the Express Request object.
// This is safer than extending the interface in a way that can cause conflicts.
declare global {
  namespace Express {
    interface Request {
      userId?: string | number;
    }
  }
}

// Interface for a Clerk user object from the Clerk SDK
export interface ClerkSdkUser {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email_addresses: { email_address: string }[];
  username: string | null;
  image_url: string;
  public_metadata: Record<string, unknown>;
  private_metadata: Record<string, unknown>;
  unsafe_metadata: Record<string, unknown>;
}

// Interface for a user object from JWT authentication
export interface JwtUser {
  id: number;
  email: string;
  username?: string;
  role?: string;
}
