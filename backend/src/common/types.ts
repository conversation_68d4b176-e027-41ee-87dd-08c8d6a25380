// Use declaration merging to add custom properties to the Express Request object.
// This is safer than extending the interface in a way that can cause conflicts.
declare global {
  namespace Express {
    interface Request {
      userId?: string | number;
      rawBody?: string;
    }
  }
}

// Interface for a Clerk user object
export interface ClerkUser {
  id: string;
  email_addresses: { email_address: string }[];
  first_name: string | null;
  last_name: string | null;
  username: string | null;
  image_url: string | null;
  public_metadata: Record<string, unknown>;
}
