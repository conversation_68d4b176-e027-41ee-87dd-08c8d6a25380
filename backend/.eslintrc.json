{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "env": {"node": true, "es2020": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:node/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "plugins": ["@typescript-eslint", "import", "node", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": false}], "node/no-missing-import": "off", "node/no-unsupported-features/es-syntax": "off", "node/no-extraneous-import": "off", "node/no-unpublished-import": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "import/no-unresolved": "off", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "error"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "ignorePatterns": ["dist", "node_modules", "coverage", "*.js", "src/generated", "src/next"]}