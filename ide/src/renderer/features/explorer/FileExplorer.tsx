import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FileSystemItem } from '../../types/FileTypes';
import { useProject } from '../../contexts/ProjectContext';
import { useEditorContext } from '../../contexts/EditorContext';
import { useStatusContext } from '../../contexts/StatusContext';
import { useFocusContext, FocusableComponent, FocusPriority } from '../../contexts/FocusContext';
import FileCard from './FileCard';
import ContextMenu from '../../components/ContextMenu';
import InputDialog from '../../components/InputDialog';
import { isBinaryFile } from '../../utils/fileUtils';
import { ChevronDownIcon, ChevronRightIcon } from '../../utils/vsCodeIcons';
import fileClipboardService from '../../services/FileClipboardService';
import '../../styles/files.css';

// Import the getFileExplorerAPI helper function
const getFileExplorerAPI = () => {
  if (!window.electronAPI) {
    throw new Error('Electron API not available');
  }
  return window.electronAPI.fileExplorer;
};

// Define FileType for FileCard props
type FileType = 'Dir' | 'File' | 'Md' | 'Img' | 'Txt' | 'Js' | 'Jsx' | 'Ts' | 'Tsx' | 'Py' | 'Html' | 'Css' | 'Scss' | 'Json' | 'Yaml' | 'Git';

const FileExplorer: React.FC = () => {
  const { projectState, fileStructure, isProjectOpen, openProject, refreshFileStructure } = useProject();
  const { editorState, setCurrentFile } = useEditorContext();
  const { setStatusMessage } = useStatusContext();

  // Add focus management
  const { requestFocus, hasFocus, storeCursorState, getCursorState } = useFocusContext();

  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clipboardItem, setClipboardItem] = useState(fileClipboardService.getClipboardItem());
  const [fileHealthCache, setFileHealthCache] = useState<Map<string, 'low' | 'medium' | 'high' | 'critical' | null>>(new Map());

  // Reference to the file explorer container for keyboard navigation
  const fileExplorerRef = useRef<HTMLDivElement>(null);
  
  // Keep track of cleanup function for file watcher
  const fileWatcherCleanupRef = useRef<(() => void) | null>(null);
  
  // Debouncing for file changes
  const pendingChangesRef = useRef<Set<string>>(new Set());
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    path: string;
    isDirectory: boolean;
    isRoot: boolean;
  }>({ visible: false, x: 0, y: 0, path: '', isDirectory: false, isRoot: false });

  // Input dialog state
  const [inputDialog, setInputDialog] = useState<{
    isOpen: boolean;
    title: string;
    placeholder: string;
    defaultValue: string;
    onConfirm: (value: string) => void;
  }>({
    isOpen: false,
    title: '',
    placeholder: '',
    defaultValue: '',
    onConfirm: () => {}
  });

  // Load saved expanded directories from localStorage on mount
  useEffect(() => {
    try {
      const savedExpandedDirs = localStorage.getItem('kapiExpandedDirs');
      if (savedExpandedDirs) {
        const parsedDirs = JSON.parse(savedExpandedDirs);
        if (Array.isArray(parsedDirs) && parsedDirs.length > 0) {
          setExpandedDirs(new Set(parsedDirs));
        }
      }
    } catch (error) {
      console.error('Error loading expanded directories from localStorage:', error);
    }
  }, []);

  // We don't automatically request focus when component mounts to avoid focus loops
  // Focus will be requested when the user interacts with the component

  // Monitor file clipboard changes
  useEffect(() => {
    const unsubscribe = fileClipboardService.subscribe(setClipboardItem);
    return unsubscribe;
  }, []);

  // Handle focus changes
  useEffect(() => {
    if (hasFocus(FocusableComponent.FILE_EXPLORER) && fileExplorerRef.current) {
      // Focus the DOM element when our component has focus
      fileExplorerRef.current.focus();

      // Restore cursor state if available
      const cursorState = getCursorState(FocusableComponent.FILE_EXPLORER);
      if (cursorState && cursorState.position && selectedFilePath !== String(cursorState.position.column)) {
        // Get all visible items (this is a simplified version of the one in handleKeyDown)
        const getAllVisibleItems = (_items: FileSystemItem[] = []): FileSystemItem[] => {
          if (!fileStructure || !fileStructure.children) return [];

          const result: FileSystemItem[] = [];

          const processItems = (items: FileSystemItem[]) => {
            for (const item of items) {
              result.push(item);
              if (item.type === 'directory' && expandedDirs.has(item.path) && item.children) {
                processItems(item.children);
              }
            }
          };

          processItems(fileStructure.children);
          return result;
        };

        const visibleItems = getAllVisibleItems();

        // Use the column value as an index to the visible items array
        const index = Math.min(cursorState.position.column, visibleItems.length - 1);
        if (index >= 0 && visibleItems[index]) {
          setSelectedFilePath(visibleItems[index].path);
        }
      }
    }
  }, [hasFocus, getCursorState, fileStructure, expandedDirs, selectedFilePath]);

  // Set selected file when current file in editor changes and auto-expand tree
  useEffect(() => {
    if (editorState.currentFile) {
      setSelectedFilePath(editorState.currentFile);

      // Auto-expand the tree to show the current file
      const expandPathToFile = (filePath: string) => {
        const pathParts = filePath.split('/');
        const newExpandedDirs = new Set(expandedDirs);

        // Build each parent directory path and add to expanded set
        let currentPath = '';
        for (let i = 0; i < pathParts.length - 1; i++) {
          if (i === 0) {
            currentPath = pathParts[i];
          } else {
            currentPath += '/' + pathParts[i];
          }
          newExpandedDirs.add(currentPath);
        }

        // Only update if there are new directories to expand
        if (newExpandedDirs.size > expandedDirs.size) {
          setExpandedDirs(newExpandedDirs);

          // Save to localStorage
          try {
            localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
          } catch (error) {
            console.error('Error saving expanded directories to localStorage:', error);
          }
        }
      };

      expandPathToFile(editorState.currentFile);
    }
  }, [editorState.currentFile, expandedDirs]);

  // Automatically expand directories when project is opened
  useEffect(() => {
    if (fileStructure && projectState.path && !expandedDirs.has(projectState.path)) {
      console.log('Auto-expanding root directory:', projectState.path);
      const newExpandedDirs = new Set(expandedDirs);
      newExpandedDirs.add(projectState.path);
      setExpandedDirs(newExpandedDirs);

      // Save to localStorage
      try {
        localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
      } catch (error) {
        console.error('Error saving expanded directories to localStorage:', error);
      }

      // Force load the root directory contents
      if (fileStructure.children && fileStructure.children.length === 0) {
        console.log('Root directory has no children, forcing refresh');
        refreshFileStructure();
      }
    }
  }, [fileStructure, projectState.path, expandedDirs, refreshFileStructure]);

  // Handle opening a project folder
  const handleOpenProject = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use the project context's openProject function
      const success = await openProject();

      if (success && projectState.path) {
        // Automatically expand the root directory
        setExpandedDirs(new Set([projectState.path]));
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Project folder open error:', err);
      setError(err instanceof Error ? err.message : 'Failed to open project');
      setIsLoading(false);
    }
  };

  // Helper function to load directory contents
  const loadDirectoryContents = async (dirPath: string): Promise<FileSystemItem[]> => {
    try {
      const fileExplorer = getFileExplorerAPI();
      console.log(`Loading directory contents for: ${dirPath}`);
      const contents = await fileExplorer.listDirectory({ path: dirPath });
      console.log(`Received ${contents.length} items for ${dirPath}`);
      return contents;
    } catch (err) {
      console.error('Failed to load directory contents:', err);
      setStatusMessage(`Error loading directory contents: ${dirPath}`);
      return [];
    }
  };

  // Toggle directory expansion
  const toggleDirectory = async (dirPath: string) => {
    const newExpandedDirs = new Set(expandedDirs);

    if (expandedDirs.has(dirPath)) {
      // Just collapse the directory
      newExpandedDirs.delete(dirPath);
      setExpandedDirs(newExpandedDirs);
    } else {
      // Expand directory
      newExpandedDirs.add(dirPath);
      setExpandedDirs(newExpandedDirs);

      // Check if directory contents are already loaded, only load if necessary
      const needsLoading = (() => {
        if (!fileStructure) return true;
        
        // Helper function to find directory and check if it has children
        const findDirectoryInTree = (items: FileSystemItem[] | undefined, targetPath: string): FileSystemItem | null => {
          if (!items || !Array.isArray(items)) return null;
          
          for (const item of items) {
            if (item.path === targetPath) {
              return item;
            }
            if (item.type === 'directory' && item.children) {
              const found = findDirectoryInTree(item.children, targetPath);
              if (found) return found;
            }
          }
          return null;
        };

        // Check root directory
        if (fileStructure.path === dirPath) {
          return !fileStructure.children || fileStructure.children.length === 0;
        }
        
        // Find directory in tree
        const targetDir = findDirectoryInTree(fileStructure.children, dirPath);
        return !targetDir || !targetDir.children || targetDir.children.length === 0;
      })();

      if (needsLoading) {
        try {
          setStatusMessage(`Loading contents of ${dirPath.split('/').pop()}...`);

          // Get directory contents using the fileExplorer API
          const fileExplorer = getFileExplorerAPI();
          const contents = await fileExplorer.listDirectory({ path: dirPath });

        if (fileStructure) {
          // Find the directory item in the file structure and update its children directly
          const updateDirectoryContents = (items: FileSystemItem[] | undefined): boolean => {
            if (!items || !Array.isArray(items)) return false;
            for (let i = 0; i < items.length; i++) {
              if (items[i].path === dirPath) {
                // Found the directory, update its children
                items[i].children = contents;
                return true;
              }

              // Recursively check children if this is a directory
              if (items[i].type === 'directory' && items[i].children && Array.isArray(items[i].children)) {
                if (items[i].children && updateDirectoryContents(items[i].children)) {
                  return true;
                }
              }
            }
            return false;
          };

          // Special case for root directory
          if (fileStructure.path === dirPath) {
            fileStructure.children = contents;
          } else if (fileStructure.children && Array.isArray(fileStructure.children)) {
            updateDirectoryContents(fileStructure.children);
          }

          // Force a local re-render without full tree refresh
          // Since we've mutated the fileStructure object above, we need to trigger a re-render
          // We can do this by updating any local state that will cause a re-render
          setExpandedDirs(new Set(newExpandedDirs)); // This will trigger a re-render

          setStatusMessage(`Loaded contents of ${dirPath.split('/').pop()}`);
        }
      } catch (err) {
          console.error('Failed to load directory contents:', err);
          setStatusMessage(`Error loading directory contents`);
        }
      } else {
        // Contents already loaded, just trigger re-render to show them
        setExpandedDirs(new Set(newExpandedDirs)); // Trigger re-render
      }
    }

    // Save expanded directories to localStorage
    try {
      localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
    } catch (error) {
      console.error('Error saving expanded directories to localStorage:', error);
    }
  };

  // Handle file selection
  const handleFileSelect = async (path: string) => {
    // Request focus with USER priority since this is a direct user interaction
    requestFocus(FocusableComponent.FILE_EXPLORER, 'file-select', FocusPriority.USER);

    // Store the selection state
    storeCursorState(FocusableComponent.FILE_EXPLORER, {
      position: {
        lineNumber: 1, // Not used for file explorer
        column: getIndexOfItemByPath(path), // Store the index of the item instead
      },
    });

    // Check if it's a binary file that shouldn't be opened in the editor
    if (isBinaryFile(path)) {
      // For binary files, just notify the user
      setStatusMessage(`Binary file: ${path}`);
      return;
    }

    try {
      // Set visual selection immediately for better UX
      setSelectedFilePath(path);

      // Use the EditorContext to load and display the file
      setCurrentFile(path);

      // Update status message
      setStatusMessage(`Opened file: ${path.split('/').pop()}`);

      // When opening a file, request focus for the editor
      // Use NORMAL priority to allow the user to override it if needed
      requestFocus(FocusableComponent.EDITOR, 'file-explorer', FocusPriority.NORMAL);
    } catch (err) {
      console.error('Failed to open file:', err);
      setError(`Failed to open file: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setStatusMessage(`Error opening file: ${path.split('/').pop()}`);
    }
  };

  // Helper function to get the index of an item by path
  const getIndexOfItemByPath = (path: string): number => {
    if (!fileStructure || !fileStructure.children) return 0;

    // Get all visible items
    const getAllVisibleItems = (): FileSystemItem[] => {
      const result: FileSystemItem[] = [];

      const processItems = (items: FileSystemItem[]) => {
        for (const item of items) {
          result.push(item);
          if (item.type === 'directory' && expandedDirs.has(item.path) && item.children) {
            processItems(item.children);
          }
        }
      };

      if (fileStructure && fileStructure.children) {
        processItems(fileStructure.children);
      }
      return result;
    };

    const visibleItems = getAllVisibleItems();
    return visibleItems.findIndex(item => item.path === path);
  };

  // Handle context menu
  const handleContextMenu = (event: React.MouseEvent, path: string, isDirectory: boolean) => {
    // When showing context menu, ensure file explorer has focus
    requestFocus(FocusableComponent.FILE_EXPLORER, 'context-menu', FocusPriority.USER);

    // Check if this is the root directory
    const isRoot = path === projectState.path;

    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      path,
      isDirectory,
      isRoot,
    });
  };

  // Close context menu
  const closeContextMenu = () => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  };

  // Show input dialog
  const showInputDialog = (title: string, placeholder: string, defaultValue: string = '') => {
    return new Promise<string>((resolve, reject) => {
      setInputDialog({
        isOpen: true,
        title,
        placeholder,
        defaultValue,
        onConfirm: (value: string) => {
          setInputDialog(prev => ({ ...prev, isOpen: false }));
          resolve(value);
        }
      });
    });
  };

  // Close input dialog
  const closeInputDialog = () => {
    setInputDialog(prev => ({ ...prev, isOpen: false }));
  };

  // Handle creating a new file
  const handleNewFile = async () => {
    const targetPath = contextMenu.path;
    closeContextMenu();

    try {
      const fileName = await showInputDialog('New File', 'Enter file name:', '');

      if (!fileName) {
        return;
      }

      const filePath = `${targetPath}/${fileName}`;

      // Use the Electron API to create the file
      if (window.electronAPI && window.electronAPI.fileExplorer) {
        await window.electronAPI.fileExplorer.createFile(filePath, '');

        // Show success message in status bar
        setStatusMessage(`Created file: ${fileName}`);

        // Refresh the file structure
        refreshFileStructure();

        // Expand the parent directory if it's not already expanded
        if (!expandedDirs.has(targetPath)) {
          const newExpandedDirs = new Set(expandedDirs);
          newExpandedDirs.add(targetPath);
          setExpandedDirs(newExpandedDirs);

          // Save to localStorage
          try {
            localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
          } catch (error) {
            console.error('Error saving expanded directories to localStorage:', error);
          }
        }

        // Automatically open the newly created file in the editor
        setCurrentFile(filePath);
        setSelectedFilePath(filePath);
        
        // Request focus for editor
        requestFocus(FocusableComponent.EDITOR, 'file-explorer', FocusPriority.NORMAL);
      }
    } catch (err) {
      console.error('Failed to create file:', err);
      setStatusMessage(`Error creating file`);
    }
  };

  // Handle creating a new folder
  const handleNewFolder = async () => {
    const targetPath = contextMenu.path;
    closeContextMenu();

    try {
      const folderName = await showInputDialog('New Folder', 'Enter folder name:', '');

      if (!folderName) {
        return;
      }

      const dirPath = `${targetPath}/${folderName}`;

      // Use the Electron API to create the directory
      if (window.electronAPI && window.electronAPI.fileExplorer) {
        await window.electronAPI.fileExplorer.createDirectory(dirPath);

        // Show success message in status bar
        setStatusMessage(`Created folder: ${folderName}`);

        // Refresh the file structure
        refreshFileStructure();

        // Expand the parent directory if it's not already expanded
        if (!expandedDirs.has(targetPath)) {
          const newExpandedDirs = new Set(expandedDirs);
          newExpandedDirs.add(targetPath);
          setExpandedDirs(newExpandedDirs);

          // Save to localStorage
          try {
            localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
          } catch (error) {
            console.error('Error saving expanded directories to localStorage:', error);
          }
        }
      }
    } catch (err) {
      console.error('Failed to create folder:', err);
      setStatusMessage(`Error creating folder`);
    }
  };

  // Handle deleting a file or folder
  const handleDelete = async () => {
    closeContextMenu();

    // Use setTimeout to ensure the context menu is closed before showing the confirm dialog
    setTimeout(async () => {
      const isDir = contextMenu.isDirectory;
      const confirmMessage = `Are you sure you want to delete this ${isDir ? 'folder' : 'file'}?${isDir ? ' All contents will be deleted.' : ''}`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        // Use the Electron API to delete the file or directory
        if (window.electronAPI && window.electronAPI.fileExplorer) {
          await window.electronAPI.fileExplorer.deleteFileOrDirectory(contextMenu.path);

          const itemType = contextMenu.isDirectory ? 'folder' : 'file';
          const itemName = contextMenu.path.split('/').pop() || '';

          // Show success message in status bar
          setStatusMessage(`Deleted ${itemType}: ${itemName}`);

          // If the deleted file was open in the editor, clear the editor
          if (contextMenu.path === editorState.currentFile) {
            setCurrentFile(null);
          }

          // Refresh the file structure
          refreshFileStructure();
        }
      } catch (err) {
        console.error('Failed to delete:', err);
        setStatusMessage(`Error deleting item: ${contextMenu.path.split('/').pop()}`);
      }
    }, 100);
  };

  // Handle file rename
  const handleRename = async () => {
    const currentPath = contextMenu.path;
    closeContextMenu();

    try {
      const currentName = currentPath.split('/').pop() || '';
      const nameWithoutExtension = currentName.includes('.') 
        ? currentName.substring(0, currentName.lastIndexOf('.'))
        : currentName;
      const extension = currentName.includes('.') 
        ? currentName.substring(currentName.lastIndexOf('.'))
        : '';

      const newName = await showInputDialog('Rename', 'Enter new name:', nameWithoutExtension);

      if (!newName || newName === nameWithoutExtension) {
        return;
      }

      const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'));
      const newPath = `${parentPath}/${newName}${extension}`;

      // Use the Electron API to rename the file/directory
      const fileExplorer = getFileExplorerAPI();
      const result = await fileExplorer.rename(currentPath, newPath);

      if (result.success) {
        setStatusMessage(`Renamed to: ${newName}${extension}`);

        // If the renamed file was open in the editor, update the editor
        if (currentPath === editorState.currentFile) {
          setCurrentFile(newPath);
        }

        // Refresh the file structure
        refreshFileStructure();
      } else {
        setStatusMessage(`Error renaming: ${result.error}`);
      }
    } catch (err) {
      console.error('Failed to rename:', err);
      setStatusMessage(`Error renaming item`);
    }
  };

  // Handle file/folder copy
  const handleCopy = () => {
    closeContextMenu();
    
    const fileName = contextMenu.path.split('/').pop() || '';
    fileClipboardService.copy(contextMenu.path, fileName, contextMenu.isDirectory);
    setStatusMessage(`Copied ${contextMenu.isDirectory ? 'folder' : 'file'}: ${fileName}`);
  };

  // Handle file/folder cut
  const handleCut = () => {
    closeContextMenu();
    
    const fileName = contextMenu.path.split('/').pop() || '';
    fileClipboardService.cut(contextMenu.path, fileName, contextMenu.isDirectory);
    setStatusMessage(`Cut ${contextMenu.isDirectory ? 'folder' : 'file'}: ${fileName}`);
  };

  // Handle file/folder paste
  const handlePaste = async () => {
    closeContextMenu();

    if (!fileClipboardService.hasItem()) {
      setStatusMessage('Nothing to paste');
      return;
    }

    const clipboardItem = fileClipboardService.getClipboardItem();
    if (!clipboardItem) {
      setStatusMessage('Nothing to paste');
      return;
    }

    try {
      const destinationDir = contextMenu.isDirectory ? contextMenu.path : contextMenu.path.substring(0, contextMenu.path.lastIndexOf('/'));
      
      setStatusMessage(`${clipboardItem.operation === 'copy' ? 'Copying' : 'Moving'} ${clipboardItem.name}...`);
      
      const result = await fileClipboardService.paste(destinationDir);
      
      if (result.success) {
        setStatusMessage(`${clipboardItem.operation === 'copy' ? 'Copied' : 'Moved'} ${clipboardItem.name}`);
        
        // Refresh the file structure
        refreshFileStructure();
        
        // Expand the destination directory if it's not already expanded
        if (!expandedDirs.has(destinationDir)) {
          const newExpandedDirs = new Set(expandedDirs);
          newExpandedDirs.add(destinationDir);
          setExpandedDirs(newExpandedDirs);
          
          // Save to localStorage
          try {
            localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
          } catch (error) {
            console.error('Error saving expanded directories to localStorage:', error);
          }
        }
      } else {
        setStatusMessage(`Error pasting: ${result.error}`);
      }
    } catch (err) {
      console.error('Failed to paste:', err);
      setStatusMessage('Error pasting item');
    }
  };

  // Handle copy path
  const handleCopyPath = async () => {
    closeContextMenu();
    
    try {
      // Use the electron clipboard API
      if (window.electronAPI && window.electronAPI.clipboard) {
        const success = window.electronAPI.clipboard.writeText(contextMenu.path);
        if (success) {
          setStatusMessage(`Copied path: ${contextMenu.path}`);
        } else {
          setStatusMessage('Failed to copy path to clipboard');
        }
      } else {
        // Fallback to navigator clipboard API
        await navigator.clipboard.writeText(contextMenu.path);
        setStatusMessage(`Copied path: ${contextMenu.path}`);
      }
    } catch (err) {
      console.error('Failed to copy path:', err);
      setStatusMessage('Failed to copy path to clipboard');
    }
  };

  // Refresh file structure
  const handleRefresh = async () => {
    closeContextMenu();
    setStatusMessage('Refreshing file explorer...');
    await refreshFileStructure();
    setStatusMessage('File explorer refreshed');
  };

  // Render the file tree recursively
  const renderFileTree = (items: FileSystemItem[] | null | undefined, level = 0) => {
    // Improved null/undefined checks for better debugging
    if (!items) {
      console.log(`Items is null or undefined at level ${level}`);
      return (
        <div style={{ paddingLeft: `${level * 12}px` }} className="empty-directory-message">
          <span>Empty folder (null/undefined)</span>
        </div>
      );
    }

    if (!Array.isArray(items)) {
      console.log(`Items is not an array at level ${level}:`, items);
      return (
        <div style={{ paddingLeft: `${level * 12}px` }} className="empty-directory-message">
          <span>Invalid data (not an array)</span>
        </div>
      );
    }

    if (items.length === 0) {
      return (
        <div style={{ paddingLeft: `${level * 12}px` }} className="empty-directory-message">
          <span>Empty folder (zero items)</span>
        </div>
      );
    }

    // Filter out hidden files and sort directories first
    const sortedItems = items
      .filter(item => !item.name.startsWith('.'))
      .sort((a, b) => {
        if (a.type === 'directory' && b.type !== 'directory') return -1;
        if (a.type !== 'directory' && b.type === 'directory') return 1;
        return a.name.localeCompare(b.name);
      });

    return sortedItems.map(item => {
      const isDir = item.type === 'directory';
      const isExpanded = isDir && expandedDirs.has(item.path);
      const isSelected = item.path === selectedFilePath || item.path === editorState.currentFile;
      const indentStyle = { paddingLeft: `${level * 12}px` }; // Indentation based on level

      // Get health indicator from backend data
      const getHealthIndicator = (): 'low' | 'medium' | 'high' | 'critical' | null => {
        // Only show health indicators for code files
        const codeExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.c', '.cpp', '.cs'];
        const hasCodeExtension = codeExtensions.some(ext => item.name.toLowerCase().endsWith(ext));
        
        if (!hasCodeExtension || isDir || !isProjectOpen) return null;
        
        // Check cache first
        const cached = fileHealthCache.get(item.path);
        if (cached !== undefined) {
          return cached;
        }
        
        // Fetch health data asynchronously
        if (projectState?.path) {
          window.electronAPI.codeAnalysis.getFileHealth({
            projectPath: projectState.path,
            filePath: item.path
          }).then(response => {
            if (response.success && response.data) {
              const riskLevel = response.data.riskLevel;
              setFileHealthCache(prev => new Map(prev.set(item.path, riskLevel)));
            } else {
              setFileHealthCache(prev => new Map(prev.set(item.path, null)));
            }
          }).catch(err => {
            console.warn('Failed to fetch file health:', err);
            setFileHealthCache(prev => new Map(prev.set(item.path, null)));
          });
        }
        
        // Return null while loading
        return null;
      };

      // Determine file type for FileCard prop
      const getFileType = (): FileType => {
        if (isDir) return 'Dir';
        const extension = item.name.split('.').pop()?.toLowerCase();
        switch (extension) {
          case 'md': return 'Md';
          case 'png': case 'jpg': case 'jpeg': case 'svg': case 'gif': case 'webp': return 'Img';
          case 'txt': return 'Txt';
          case 'js': return 'Js';
          case 'jsx': return 'Jsx';
          case 'ts': return 'Ts';
          case 'tsx': return 'Tsx';
          case 'py': return 'Py';
          case 'html': return 'Html';
          case 'css': return 'Css';
          case 'scss': return 'Scss';
          case 'json': return 'Json';
          case 'yaml': case 'yml': return 'Yaml';
          case 'gitignore': case 'git': return 'Git';
          // Add more file types as needed
          default: return 'File';
        }
      };

      // Handle click on the item (excluding chevron clicks)
      const handleItemClick = (e: React.MouseEvent) => {
        // CRITICAL: Stop all event propagation to prevent bubbling
        e.stopPropagation();
        e.preventDefault();


        // Focus on this component with high priority (user interaction)
        requestFocus(FocusableComponent.FILE_EXPLORER, 'item-click', FocusPriority.USER);

        // Set this file/directory as selected
        setSelectedFilePath(item.path);

        // Store the selection state
        storeCursorState(FocusableComponent.FILE_EXPLORER, {
          position: {
            lineNumber: 1,
            column: getIndexOfItemByPath(item.path),
          },
        });

        if (isDir) {
          // For directories, only select but NEVER toggle expansion
          // Expansion is ONLY handled by chevron click
        } else {
          // For files, open in editor
          handleFileSelect(item.path);
        }
      };

      // Handle chevron click for directory expansion/collapse
      const handleChevronClick = (e: React.MouseEvent) => {
        // CRITICAL: Completely stop event propagation and default behavior
        e.stopPropagation();
        e.preventDefault();
        // Note: stopImmediatePropagation doesn't exist on React SyntheticEvent
        // stopPropagation() is sufficient for React events

        toggleDirectory(item.path);
      };

      // Use React.Fragment to group the item and its potential children
      return (
        <React.Fragment key={item.path}>
          {/* Container for the item itself with indentation */}
          <div style={indentStyle}>
            <div
              className={`file-explorer-item-container ${isSelected ? 'selected-item' : ''}`}
            >
              {isDir && (
                <span
                  className="file-explorer-chevron"
                  onClick={handleChevronClick}
                  onMouseDown={(e) => {
                    // Extra protection: stop mousedown events too
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  {isExpanded ? <ChevronDownIcon size={16} /> : <ChevronRightIcon size={16} />}
                </span>
              )}
              {!isDir && <span className="file-explorer-chevron-placeholder"></span>}
              <FileCard
                name={item.name}
                path={item.path}
                fileType={getFileType()}
                expandable={isDir}
                isSelected={isSelected}
                healthIndicator={getHealthIndicator()}
                onSelect={handleItemClick}
                onContextMenu={(e) => {
                  // Ensure context menu doesn't trigger collapse
                  e.stopPropagation();
                  handleContextMenu(e, item.path, isDir);
                }}
              />
            </div>
          </div>

          {/* Render children *after* the parent item's container */}
          {isDir && isExpanded && (
            <div className="directory-children-container">
              {/* Directory children will be rendered here */}

              {/* Choose what to render based on children state */}
              {(() => {
                // If item has children property
                if (item.children) {
                  // If children is an array
                  if (Array.isArray(item.children)) {
                    // If array has items
                    if (item.children.length > 0) {
                      return renderFileTree(item.children, level + 1);
                    } else {
                      // Empty array
                      return (
                        <div style={{ ...indentStyle, paddingLeft: `${(level + 1) * 12}px` }} className="empty-directory-message">
                          <span>Empty folder (array with 0 items)</span>
                        </div>
                      );
                    }
                  } else {
                    // Children exists but is not an array
                    return (
                      <div style={{ ...indentStyle, paddingLeft: `${(level + 1) * 12}px` }} className="empty-directory-message">
                        <span>Invalid children format (not an array)</span>
                      </div>
                    );
                  }
                } else {
                  // No children property - try to load it on demand
                  return (
                    <div style={{ ...indentStyle, paddingLeft: `${(level + 1) * 12}px` }} className="empty-directory-message">
                      <span>Loading directory contents...</span>
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();
                          setStatusMessage(`Reloading ${item.name}...`);

                          try {
                            const fileExplorer = getFileExplorerAPI();
                            console.log(`Reloading directory: ${item.path}`);
                            const contents = await fileExplorer.listDirectory({ path: item.path });
                            console.log(`Reload received ${contents.length} items for ${item.path}:`, contents);

                            // Update the item directly with the contents
                            item.children = contents;

                            // Force a re-render
                            refreshFileStructure();

                            // Make sure this directory stays expanded
                            const newExpandedDirs = new Set(expandedDirs);
                            newExpandedDirs.add(item.path);
                            setExpandedDirs(newExpandedDirs);

                            // Save to localStorage
                            try {
                              localStorage.setItem('kapiExpandedDirs', JSON.stringify([...newExpandedDirs]));
                            } catch (storageErr) {
                              console.error('Error saving expanded directories to localStorage:', storageErr);
                            }

                            setStatusMessage(`Loaded ${item.name}`);
                          } catch (err) {
                            console.error(`Failed to reload directory: ${item.path}`, err);
                            setStatusMessage(`Error reloading ${item.name}`);
                          }
                        }}
                        style={{ marginLeft: '10px', fontSize: '0.8rem', padding: '2px 5px' }}
                      >
                        Reload
                      </button>
                    </div>
                  );
                }
              })()}
            </div>
          )}
        </React.Fragment>
      );
    });
  }; // End of renderFileTree function

  // Debounced refresh function
  const debouncedRefresh = useCallback(() => {
    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new timer
    debounceTimerRef.current = setTimeout(() => {
      if (pendingChangesRef.current.size > 0) {
        console.log(`[FileExplorer] Processing ${pendingChangesRef.current.size} pending changes`);
        
        // Clear pending changes
        pendingChangesRef.current.clear();
        
        // Perform the refresh
        refreshFileStructure();
      }
    }, 500); // Wait 500ms after the last change before refreshing
  }, [refreshFileStructure]);

  // Set up file system watching
  useEffect(() => {
    if (!isProjectOpen || !projectState.path) {
      return;
    }

    const setupFileWatcher = async () => {
      try {
        const fileExplorer = getFileExplorerAPI();
        
        // Start watching the project directory
        const result = await fileExplorer.startWatching(projectState.path);
        
        if (result.success) {
          // Set up the file change listener with intelligent handling
          const cleanup = fileExplorer.onFileChange((event) => {
            // File change detected (reduced logging)
            
            // Skip certain files that change frequently but don't affect the tree
            const ignoredPatterns = [
              /\.DS_Store$/,
              /\.swp$/,
              /\.swo$/,
              /~$/,
              /\.tmp$/,
              /\.log$/
            ];
            
            const shouldIgnore = ignoredPatterns.some(pattern => pattern.test(event.path));
            if (shouldIgnore) {
              console.log(`[FileExplorer] Ignoring change to: ${event.path}`);
              return;
            }
            
            // Add to pending changes
            pendingChangesRef.current.add(event.path);
            
            // Trigger debounced refresh
            debouncedRefresh();
          });
          
          // Store the cleanup function
          fileWatcherCleanupRef.current = cleanup;
        } else {
          console.error('[FileExplorer] Failed to start file watcher:', result.error);
        }
      } catch (err) {
        console.error('[FileExplorer] Error setting up file watcher:', err);
      }
    };

    setupFileWatcher();

    // Cleanup function
    return () => {
      // Clear any pending debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      
      if (fileWatcherCleanupRef.current) {
        fileWatcherCleanupRef.current();
        fileWatcherCleanupRef.current = null;
      }
      
      // Stop watching when component unmounts or project changes
      const fileExplorer = getFileExplorerAPI();
      fileExplorer.stopWatching().catch(err => {
        console.error('[FileExplorer] Error stopping file watcher:', err);
      });
    };
  }, [isProjectOpen, projectState.path, debouncedRefresh]);

  // Add a listener for the refreshFileExplorer event
  useEffect(() => {
    const handleRefreshEvent = () => {
      console.log('Received refreshFileExplorer event');
      // Don't trigger if we're already debouncing file changes
      if (pendingChangesRef.current.size === 0) {
        refreshFileStructure();
      }
    };

    document.addEventListener('refreshFileExplorer', handleRefreshEvent);
    return () => {
      document.removeEventListener('refreshFileExplorer', handleRefreshEvent);
    };
  }, [refreshFileStructure]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!fileStructure || !fileStructure.children) return;

    // Request focus when keyboard navigation is used
    requestFocus(FocusableComponent.FILE_EXPLORER, 'keyboard-nav', FocusPriority.USER);

    // Find all visible items in the current view
    const getAllVisibleItems = (items: FileSystemItem[], result: FileSystemItem[] = []): FileSystemItem[] => {
      for (const item of items) {
        result.push(item);
        if (item.type === 'directory' && expandedDirs.has(item.path) && item.children) {
          getAllVisibleItems(item.children, result);
        }
      }
      return result;
    };

    const visibleItems = getAllVisibleItems(fileStructure.children);

    // Find the current selected item index
    const currentIndex = visibleItems.findIndex(item => item.path === selectedFilePath);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (currentIndex < visibleItems.length - 1) {
          const newPath = visibleItems[currentIndex + 1].path;
          setSelectedFilePath(newPath);

          // Store the new selection state
          storeCursorState(FocusableComponent.FILE_EXPLORER, {
            position: {
              lineNumber: 1,
              column: currentIndex + 1,
            },
          });
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (currentIndex > 0) {
          const newPath = visibleItems[currentIndex - 1].path;
          setSelectedFilePath(newPath);

          // Store the new selection state
          storeCursorState(FocusableComponent.FILE_EXPLORER, {
            position: {
              lineNumber: 1,
              column: currentIndex - 1,
            },
          });
        }
        break;
      case 'ArrowRight':
        e.preventDefault();
        if (selectedFilePath) {
          const selectedItem = visibleItems.find(item => item.path === selectedFilePath);
          if (selectedItem && selectedItem.type === 'directory' && !expandedDirs.has(selectedFilePath)) {
            toggleDirectory(selectedFilePath);
          }
        }
        break;
      case 'ArrowLeft':
        e.preventDefault();
        if (selectedFilePath) {
          const selectedItem = visibleItems.find(item => item.path === selectedFilePath);
          if (selectedItem && selectedItem.type === 'directory' && expandedDirs.has(selectedFilePath)) {
            toggleDirectory(selectedFilePath);
          } else {
            // If not a directory or not expanded, go to parent directory
            const parentPath = selectedFilePath.substring(0, selectedFilePath.lastIndexOf('/'));
            if (parentPath && parentPath !== selectedFilePath) {
              setSelectedFilePath(parentPath);

              // Store the new selection state
              const parentIndex = visibleItems.findIndex(item => item.path === parentPath);
              if (parentIndex >= 0) {
                storeCursorState(FocusableComponent.FILE_EXPLORER, {
                  position: {
                    lineNumber: 1,
                    column: parentIndex,
                  },
                });
              }
            }
          }
        }
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedFilePath) {
          const selectedItem = visibleItems.find(item => item.path === selectedFilePath);
          if (selectedItem) {
            if (selectedItem.type === 'directory') {
              toggleDirectory(selectedFilePath);
            } else {
              handleFileSelect(selectedFilePath);
            }
          }
        }
        break;
    }
  };

  // Add click handler to request focus when clicking anywhere in the component
  const handleContainerClick = (e: React.MouseEvent) => {
    // Only handle clicks directly on the container, not on child elements
    if (e.target === e.currentTarget) {
      requestFocus(FocusableComponent.FILE_EXPLORER, 'container-click', FocusPriority.USER);
    }
  };

  // Handle context menu on empty areas to create files in project root
  const handleContainerContextMenu = (e: React.MouseEvent) => {
    // Check if the click is on an empty area (not on a file/folder)
    const target = e.target as HTMLElement;
    const isEmptyArea = target.classList.contains('file-tree-content') || 
                       target.classList.contains('file-tree') ||
                       target.classList.contains('file-explorer');
    
    if (isEmptyArea && projectState.path) {
      e.preventDefault();
      e.stopPropagation();
      
      handleContextMenu(e, projectState.path, true);
    }
  };

  return (
    <div
      className="file-explorer"
      ref={fileExplorerRef}
      tabIndex={0} // Make the div focusable for keyboard events
      onKeyDown={handleKeyDown}
      onClick={handleContainerClick}
      data-testid="file-explorer"
    >
      {!isProjectOpen ? (
        <div className="file-explorer-empty">
          <p>No project open</p>
          <button className="open-project-button" onClick={handleOpenProject} disabled={isLoading}>
            {isLoading ? 'Opening...' : 'Open Project Folder'}
          </button>
          {error && <div style={{ color: '#e74c3c', marginTop: '10px' }}>{error}</div>}
        </div>
      ) : (
        <div className="file-tree" onContextMenu={handleContainerContextMenu}>
          <div className="file-explorer-header">
            <h3 className="file-explorer-title">Files</h3>
            <div className="file-explorer-actions">
              <button
              className="file-explorer-button"
              onClick={async () => {
              setStatusMessage('Refreshing file structure...');
              await refreshFileStructure();
                setStatusMessage('File structure refreshed');
              }}
              disabled={isLoading}
              title="Refresh"
              >
              {isLoading ? '...' : '🔄'}
              </button>
            </div>
          </div>

          {isLoading && !fileStructure && (
            <div className="file-explorer-empty">
              <p>Loading project files...</p>
              <div className="loading-spinner"></div>
            </div>
          )}

          {error && (
            <div className="file-explorer-empty">
              <p style={{ color: '#e74c3c' }}>{error}</p>
              <button
                className="open-project-button"
                onClick={refreshFileStructure}
                style={{ marginTop: '10px' }}
              >
                Try Again
              </button>
            </div>
          )}

          {fileStructure && (
            <div className="file-tree-content" onContextMenu={handleContainerContextMenu}>
              {fileStructure.children && fileStructure.children.length > 0 ? (
                renderFileTree(fileStructure.children)
              ) : (
                <div className="file-explorer-empty" style={{ padding: '20px' }} onContextMenu={handleContainerContextMenu}>
                  <p>This folder is empty</p>
                  <p style={{ fontSize: '0.9em', color: 'var(--text-dim)', marginTop: '10px' }}>
                    Right-click to create files and folders
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Context Menu */}
      {contextMenu.visible && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          isDirectory={contextMenu.isDirectory}
          isRoot={contextMenu.isRoot}
          onNewFile={handleNewFile}
          onNewFolder={handleNewFolder}
          onDelete={handleDelete}
          onRename={handleRename}
          onCopy={handleCopy}
          onCut={handleCut}
          onPaste={handlePaste}
          onCopyPath={handleCopyPath}
          onRefresh={handleRefresh}
          onClose={closeContextMenu}
        />
      )}

      {/* Input Dialog */}
      <InputDialog
        isOpen={inputDialog.isOpen}
        title={inputDialog.title}
        placeholder={inputDialog.placeholder}
        defaultValue={inputDialog.defaultValue}
        onConfirm={inputDialog.onConfirm}
        onCancel={closeInputDialog}
      />
    </div>
  );
};

export default FileExplorer;
