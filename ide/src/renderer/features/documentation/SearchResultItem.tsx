import React, { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { MermaidViewer } from '../editor/viewers/MermaidViewer';

const ItemContainer = styled.div`
  background-color: ${props => props.theme.colors.backgroundSecondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease-in-out;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`;

const Title = styled.h4`
  margin: 0;
  font-size: 1.1em;
  color: ${props => props.theme.colors.text};
`;

const Purpose = styled.p`
  margin: 5px 0 0;
  font-style: italic;
  color: ${props => props.theme.colors.textSecondary};
`;

const DetailsContainer = styled.div`
  margin-top: 15px;
  border-top: 1px solid ${props => props.theme.colors.border};
  padding-top: 15px;
`;

const Explanation = styled.div`
  margin-bottom: 15px;
`;

// Define the structure of a search result
interface SearchResult {
  id: number;
  unitName: string;
  unitType: string;
  path: string;
  purpose: string;
  humanReadableExplanation: string;
  visualDiagram: string;
  similarity: number;
}

interface SearchResultItemProps {
  result: SearchResult;
}

const SearchResultItem: React.FC<SearchResultItemProps> = ({ result }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <ItemContainer>
      <Header onClick={() => setIsExpanded(!isExpanded)}>
        <div>
          <Title>{result.unitName} ({result.unitType})</Title>
          <Purpose>{result.purpose}</Purpose>
        </div>
        <FontAwesomeIcon icon={isExpanded ? faChevronDown : faChevronRight} />
      </Header>
      {isExpanded && (
        <DetailsContainer>
          <Explanation>
            <p><strong>Path:</strong> <code>{result.path}</code></p>
            <p><strong>Explanation:</strong> {result.humanReadableExplanation}</p>
          </Explanation>
          <MermaidViewer diagram={result.visualDiagram} />
        </DetailsContainer>
      )}
    </ItemContainer>
  );
};

export default SearchResultItem;
