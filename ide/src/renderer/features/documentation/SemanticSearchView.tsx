import React from 'react';
import styled from 'styled-components';
import { SearchBar } from '../../components/SearchBar';
import { useSemanticSearch } from '../../contexts/SemanticSearchContext';
import SearchResultItem from './SearchResultItem';

const SearchViewContainer = styled.div`
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const ResultsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const LoadingSpinner = styled.div`
  border: 4px solid ${props => props.theme.colors.border};
  border-top: 4px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  align-self: center;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  text-align: center;
`;

const NoResultsMessage = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  text-align: center;
  padding: 20px;
`;

const SemanticSearchView: React.FC = () => {
  const {
    query,
    setQuery,
    results,
    isLoading,
    error,
    executeSearch,
  } = useSemanticSearch();

  const handleSearch = () => {
    executeSearch();
  };

  return (
    <SearchViewContainer>
      <SearchBar
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onSearch={handleSearch}
        placeholder="Search documentation with natural language..."
      />
      {isLoading && <LoadingSpinner />}
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {!isLoading && !error && (
        <ResultsContainer>
          {results.length > 0 ? (
            results.map(result => (
              <SearchResultItem key={result.id} result={result} />
            ))
          ) : (
            <NoResultsMessage>
              No results found. Try a different query.
            </NoResultsMessage>
          )}
        </ResultsContainer>
      )}
    </SearchViewContainer>
  );
};

export default SemanticSearchView;

