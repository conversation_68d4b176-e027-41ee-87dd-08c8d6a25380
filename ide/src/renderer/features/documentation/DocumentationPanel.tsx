import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { fontFamilies } from '../../utils/fontConfig';
import { sanitizeHtml } from '../../utils/fileUtils';

const Container = styled.div`
  background-color: #1e1e1e;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  font-family: ${fontFamilies.ui};
`;

const Header = styled.div`
  background-color: #333333;
  border-radius: 5px 5px 0 0;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  align-items: center;
`;

const Title = styled.div`
  font-weight: bold;
  font-size: 14px;
  flex: 1;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding: 15px;
`;

const EmptyState = styled.div`
  align-items: center;
  color: #666;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  font-size: 36px;
  margin-bottom: 15px;
  opacity: 0.6;
`;

const EmptyStateText = styled.div`
  font-size: 14px;
  max-width: 300px;
`;

const Button = styled.button`
  background-color: #007acc;
  border: none;
  border-radius: 5px;
  color: white;
  cursor: pointer;
  font-size: 12px;
  margin-top: 15px;
  padding: 8px 15px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #0062a3;
  }
`;

// Component to render sanitized documentation content safely
const DocumentationContent = ({ htmlContent }: { htmlContent: string }) => {
  // Create sanitized html
  const sanitizedHtml = sanitizeHtml(htmlContent);
  
  return (
    <div 
      className="documentation-content"
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }} 
    />
  );
};

interface DocumentationPanelProps {
  projectId: string;
}

const DocumentationPanel: React.FC<DocumentationPanelProps> = ({ projectId }) => {
  const [documentation, setDocumentation] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Simulated effect to load documentation
  useEffect(() => {
    const loadDocumentation = async () => {
      if (!projectId) return;
      
      setIsLoading(true);
      
      try {
        // In a real implementation, this would fetch from an API
        // Simulate API call with timeout
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // For now, return empty to show placeholder
        setDocumentation(null);
      } catch (error) {
        console.error('Failed to load documentation:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadDocumentation();
  }, [projectId]);

  const handleCreateDocumentation = () => {
    // In a real implementation, this would open a documentation creation flow
    alert('Documentation creation would be initiated here');
  };

  return (
    <Container>
      <Header>
        <Title>Documentation</Title>
      </Header>
      
      <ContentArea>
        {isLoading ? (
          <EmptyState>
            <EmptyStateText>Loading documentation...</EmptyStateText>
          </EmptyState>
        ) : documentation ? (
          <DocumentationContent htmlContent={documentation} />
        ) : (
          <EmptyState>
            <EmptyStateIcon>📄</EmptyStateIcon>
            <EmptyStateText>
              This project doesn't have any documentation yet. Following the backwards build approach, 
              documentation should be created before code implementation.
            </EmptyStateText>
            <Button onClick={handleCreateDocumentation}>
              Create Documentation
            </Button>
          </EmptyState>
        )}
      </ContentArea>
    </Container>
  );
};

export default DocumentationPanel;