import axios from 'axios';
// API base URL for making requests to the backend
const API_BASE_URL = 'http://localhost:3000/api';

/**
 * Request parameters for documentation generation
 */
interface GenerateDocumentationRequest {
  path: string;
  type: 'file' | 'directory';
}

interface GenerateDocumentationResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  generatedFiles?: string[];
}

/**
 * Service for interacting with the documentation API
 */
class DocumentationService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/documentation`;
  }

  /**
   * Generate documentation for a file or directory from the IDE
   * @param path - The path to the file or directory
   * @param type - Whether the path is a file or directory
   * @returns Response with success status and generated files info
   */
  async generateFromIde(
    path: string,
    type: 'file' | 'directory'
  ): Promise<GenerateDocumentationResponse> {
    try {
      const response = await axios.post<GenerateDocumentationResponse>(
        `${this.baseUrl}/generate-from-ide`,
        { path, type },
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        return error.response.data as GenerateDocumentationResponse;
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }
}

// Export as a singleton
const documentationService = new DocumentationService();
export default documentationService;
