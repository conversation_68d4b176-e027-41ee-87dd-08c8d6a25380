/**
 * DocumentationApi.ts
 * 
 * Service for managing documentation-related API requests.
 * Provides methods for generating, updating, and retrieving documentation for projects.
 * 
 * Maps to the /documentation/* endpoints in the backend.
 */

import apiClient from './ApiClient';

// Type definitions
export interface DocumentationRequest {
  project_id: number;
  file_path?: string;
  code_content?: string;
  doc_type?: 'readme' | 'api' | 'jsdoc' | 'user_guide' | 'architecture';
  format?: 'markdown' | 'html' | 'plain';
}

export interface DocumentationResponse {
  id: number;
  project_id: number;
  doc_type: string;
  content: string;
  format: string;
  created_at: string;
  updated_at: string;
}

class DocumentationApi {
  /**
   * Generates documentation for a project or specific file
   */
  async generateDocumentation(request: DocumentationRequest): Promise<DocumentationResponse> {
    // Implementation will be added later
    return {} as DocumentationResponse;
  }
  
  /**
   * Gets documentation for a project
   */
  async getProjectDocumentation(projectId: number, docType?: string): Promise<DocumentationResponse[]> {
    // Implementation will be added later
    return [];
  }
  
  /**
   * Gets documentation for a specific file
   */
  async getFileDocumentation(projectId: number, filePath: string): Promise<DocumentationResponse> {
    // Implementation will be added later
    return {} as DocumentationResponse;
  }
  
  /**
   * Updates existing documentation
   */
  async updateDocumentation(docId: number, content: string): Promise<DocumentationResponse> {
    // Implementation will be added later
    return {} as DocumentationResponse;
  }
  
  /**
   * Syncs documentation with current code
   */
  async syncDocumentation(projectId: number, docType?: string): Promise<DocumentationResponse[]> {
    // Implementation will be added later
    return [];
  }
}

// Export a singleton instance
export const documentationApi = new DocumentationApi();
export default documentationApi;
