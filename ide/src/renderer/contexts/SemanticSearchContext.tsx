import React, { createContext, useContext, useState, ReactNode } from 'react';
import { searchDocs } from '../../services/DocumentationApi'; // We will create this next

// Define the structure of a search result
interface SearchResult {
  id: number;
  unitName: string;
  unitType: string;
  path: string;
  purpose: string;
  humanReadableExplanation: string;
  visualDiagram: string;
  similarity: number;
}

// Define the context state
interface SemanticSearchContextState {
  query: string;
  results: SearchResult[];
  isLoading: boolean;
  error: string | null;
  setQuery: (query: string) => void;
  executeSearch: () => Promise<void>;
}

const SemanticSearchContext = createContext<SemanticSearchContextState | undefined>(undefined);

export const SemanticSearchProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeSearch = async () => {
    if (!query) return;
    setIsLoading(true);
    setError(null);
    try {
      const searchResults = await searchDocs(query);
      setResults(searchResults);
    } catch (err) {
      setError('Failed to fetch search results. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    query,
    results,
    isLoading,
    error,
    setQuery,
    executeSearch,
  };

  return (
    <SemanticSearchContext.Provider value={value}>
      {children}
    </SemanticSearchContext.Provider>
  );
};

export const useSemanticSearch = (): SemanticSearchContextState => {
  const context = useContext(SemanticSearchContext);
  if (context === undefined) {
    throw new Error('useSemanticSearch must be used within a SemanticSearchProvider');
  }
  return context;
};
