import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect, useRef } from 'react';
import { FileSystemItem } from '../types/FileTypes';
import communityService, { User } from '../services/communityService';


// Define a helper interface for the fileExplorer API
interface FileExplorerAPI {
  selectDirectory: () => Promise<string | { canceled: boolean; dirPath?: string }>;
  listDirectory: (options: { path: string }) => Promise<FileSystemItem[]>;
  readFile: (options: { path: string }) => Promise<string>;
  createFile: (filePath: string, content: string) => Promise<void>;
  createDirectory: (dirPath: string) => Promise<void>;
  deleteFileOrDirectory: (path: string) => Promise<void>;
  startWatching: (path: string, interval?: number) => void;
  stopWatching: () => void;
  onFileChange: (callback: () => void) => () => void;
}

// Helper function to get the properly typed API
function getFileExplorerAPI(): FileExplorerAPI {
  if (!window.electronAPI) {
    throw new Error('Electron API not available - this feature requires desktop app');
  }

  return window.electronAPI.fileExplorer;
}

// Export the interface
export interface ProjectState {
  name: string;
  path: string;
  fileTree: FileSystemItem[];
  recentFiles: string[];
  lastOpenedFiles: {[key: string]: string}; // path -> content
  panels: {
    leftPanelWidth: string;
    rightPanelWidth: string;
    bottomPanelHeight: string;
    leftPanelCollapsed: boolean;
    rightPanelCollapsed: boolean;
    bottomPanelCollapsed: boolean;
  };
  metadata: {
    createdAt: string;
    lastModified: string;
    backwardsBuildProgress: number;
    description: string;
  };
  git: {
    isGitRepository: boolean;
    repositoryName: string | null;
    currentBranch: string | null;
  };
}

// Default initial state
const defaultProjectState: ProjectState = {
  name: 'New Project',
  path: '',
  fileTree: [],
  recentFiles: [],
  lastOpenedFiles: {},
  panels: {
    leftPanelWidth: '300px',
    rightPanelWidth: '300px',
    bottomPanelHeight: '190px',
    leftPanelCollapsed: false,
    rightPanelCollapsed: false,
    bottomPanelCollapsed: false
  },
  metadata: {
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    backwardsBuildProgress: 0,
    description: ''
  },
  git: {
    isGitRepository: false,
    repositoryName: null,
    currentBranch: null
  }
};

// Export the interface
export interface ProjectContextType {
  projectState: ProjectState;
  fileStructure: FileSystemItem | null;
  isProjectOpen: boolean;
  createProject: (name: string, path: string, description?: string) => Promise<boolean>;
  openProject: () => Promise<boolean>;
  saveProject: () => Promise<boolean>;
  refreshFileStructure: () => Promise<void>;
  updateProjectState: (newState: Partial<ProjectState>) => void;
  updatePanelState: (panelUpdates: Partial<ProjectState['panels']>) => void;
  addRecentFile: (filePath: string) => void;
  updateFileContent: (filePath: string, content: string) => void;
  currentUser: User | null;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [projectState, setProjectState] = useState<ProjectState>(defaultProjectState);
  const [fileStructure, setFileStructure] = useState<FileSystemItem | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProjectOpen, setIsProjectOpen] = useState(false);

  // Add a ref to track if we've already attempted to load a project
  const didInitialLoadRef = useRef(false);

  // Create a new project
  const createProject = useCallback(async (name: string, path: string, description = ''): Promise<boolean> => {
    try {
      // Create a new project state
      const newProjectState: ProjectState = {
        ...defaultProjectState,
        name: name,
        path: path,
        metadata: {
          ...defaultProjectState.metadata,
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          description
        }
      };

      // Save to localStorage
      localStorage.setItem('lastProjectPath', path);

      // Update state
      setProjectState(newProjectState);
      setIsProjectOpen(true);

      // Initialize AST service for structure search
      try {
        if (window.electronAPI?.ast?.initializeProject) {
          await window.electronAPI.ast.initializeProject({ path });
          console.log('[Project] AST service initialized for new project:', path);
        }
      } catch (error) {
        console.warn('[Project] Failed to initialize AST service:', error);
        // Don't fail project creation if AST initialization fails
      }

      // Create root directory structure
      try {
        // Get directory contents (array is guaranteed by our API wrapper)
        const fileExplorer = getFileExplorerAPI();
        const contents = await fileExplorer.listDirectory({ path });
        const rootStructure: FileSystemItem = {
          name: path.split('/').pop() || 'Project',
          path,
          type: 'directory',
          children: contents
        };
        setFileStructure(rootStructure);
      } catch (error) {
        console.warn('Could not list directory contents:', error);
      }

      return true;
    } catch (error) {
      console.error('Failed to create project:', error);
      return false;
    }
  }, []);

  // Open a project directory
  const openProject = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Check if electron API is available
      // @ts-ignore - Electron API is injected at runtime
      if (!window.electronAPI) {
        // In web environment, provide a mock or fallback
        console.warn('Electron API not available - running in web environment');
        setIsLoading(false);
        return false;
      }

      // Show directory selection dialog
      // Use the fileExplorer API instead of direct invoke
      const fileExplorer = getFileExplorerAPI();
      const result = await fileExplorer.selectDirectory();

      // Handle both string and object response formats
      if (!result || (typeof result === 'object' && 'canceled' in result && result.canceled === true)) {
        setIsLoading(false);
        return false;
      }

      // The result could be a string or an object with dirPath
      const dirPath = typeof result === 'string' ? result : result.dirPath as string;

      // Get directory contents using the fileExplorer API
      try {
        // The listDirectory function now returns a properly formatted array
        const fileExplorer = getFileExplorerAPI();
        const contents = await fileExplorer.listDirectory({ path: dirPath });

        // Create root structure
        const rootStructure: FileSystemItem = {
          name: dirPath.split('/').pop() || 'Project',
          path: dirPath,
          type: 'directory',
          // The contents are now directly an array
          children: contents
        };

        // Update project state
        setProjectState(prev => ({
          ...prev,
          name: rootStructure.name,
          path: dirPath,
          fileTree: [rootStructure],
          metadata: {
            ...prev.metadata,
            lastModified: new Date().toISOString()
          }
        }));

        setFileStructure(rootStructure);
        setIsProjectOpen(true);

        // Save last opened project to localStorage
        localStorage.setItem('lastProjectPath', dirPath);

        // Dispatch an event to notify components
        document.dispatchEvent(new CustomEvent('projectOpened', {
          detail: {
            path: dirPath
          }
        }));

        // Initialize AST service for structure search
        try {
          if (window.electronAPI?.ast?.initializeProject) {
            await window.electronAPI.ast.initializeProject({ path: dirPath });
            console.log('[Project] AST service initialized for project:', dirPath);
          }
        } catch (error) {
          console.warn('[Project] Failed to initialize AST service:', error);
          // Don't fail project opening if AST initialization fails
        }

        setIsLoading(false);
        return true;
      } catch (error) {
        console.error('Failed to list directory contents:', error);
        throw error;
      }
    } catch (err) {
      console.error('Failed to open project:', err);
      setIsLoading(false);
      return false;
    }
  }, []);

  // Refresh file structure
  const refreshFileStructure = useCallback(async (): Promise<void> => {
    if (!projectState.path || projectState.path === '') {
      return;
    }

    try {
      setIsLoading(true);

      // Get expanded directories from localStorage
      let expandedDirs: Set<string> = new Set();
      try {
        const savedExpandedDirs = localStorage.getItem('kapiExpandedDirs');
        if (savedExpandedDirs) {
          const parsedDirs = JSON.parse(savedExpandedDirs);
          if (Array.isArray(parsedDirs)) {
            expandedDirs = new Set(parsedDirs);
          }
        }
      } catch (error) {
        console.error('Error loading expanded directories:', error);
      }

      // Recursive function to load directory structure with expanded directories
      const loadDirectoryRecursive = async (dirPath: string): Promise<FileSystemItem> => {
        const fileExplorer = getFileExplorerAPI();
        const contents = await fileExplorer.listDirectory({ path: dirPath });
        
        // Process each item and load expanded directories recursively
        const processedContents = await Promise.all(
          contents.map(async (item) => {
            if (item.type === 'directory' && expandedDirs.has(item.path)) {
              // This directory is expanded, load its contents recursively
              try {
                const subContents = await fileExplorer.listDirectory({ path: item.path });
                const processedSubContents = await Promise.all(
                  subContents.map(async (subItem) => {
                    if (subItem.type === 'directory' && expandedDirs.has(subItem.path)) {
                      // Recursively load this subdirectory too
                      return await loadDirectoryRecursive(subItem.path);
                    }
                    return subItem;
                  })
                );
                return { ...item, children: processedSubContents };
              } catch (err) {
                console.error(`Failed to load contents of ${item.path}:`, err);
                return { ...item, children: [] };
              }
            }
            return item;
          })
        );

        return {
          name: dirPath.split('/').pop() || 'Directory',
          path: dirPath,
          type: 'directory',
          children: processedContents
        };
      };

      try {
        // Load the entire structure with expanded directories
        const rootStructure = await loadDirectoryRecursive(projectState.path);
        
        setFileStructure(rootStructure);

        // Update the fileTree in the project state
        setProjectState(prev => ({
          ...prev,
          fileTree: [rootStructure],
          metadata: {
            ...prev.metadata,
            lastModified: new Date().toISOString()
          }
        }));

        // Note: Removed refreshFileExplorer event dispatch to prevent circular loops
        // FileExplorer has its own file watching with proper debouncing
      } catch (error) {
        console.error('Failed to refresh directory contents:', error);
        throw error;
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Failed to refresh file structure:', err);
      setIsLoading(false);
    }
  }, [projectState.path]);

  // Save the current project
  const saveProject = useCallback(async (): Promise<boolean> => {
    try {
      if (!isProjectOpen || !projectState.path) {
        console.warn('Cannot save project: No project open or no project path');
        return false;
      }

      const kapiDirectory = `${projectState.path}/.kapi`;
      console.log(`Saving project to: ${kapiDirectory}/project.json`);

      // Ensure .kapi directory exists
      try {
        const fileExplorer = getFileExplorerAPI();
        await fileExplorer.createDirectory(kapiDirectory);
      } catch (error) {
        console.error('Failed to create .kapi directory:', error);
        return false;
      }

      // Update last modified timestamp
      const updatedState = {
        ...projectState,
        metadata: {
          ...projectState.metadata,
          lastModified: new Date().toISOString()
        }
      };

      // Save project state to file
      try {
        const stateJson = JSON.stringify(updatedState, null, 2);
        const fileExplorer = getFileExplorerAPI();
        await fileExplorer.createFile(`${kapiDirectory}/project.json`, stateJson);

        // Update state after successful save
        setProjectState(updatedState);
        return true;
      } catch (error) {
        console.error('Failed to save project state:', error);
        return false;
      }
    } catch (error) {
      console.error('Failed to save project:', error);
      return false;
    }
  }, [isProjectOpen, projectState]);

  // Update project state with partial changes
  const updateProjectState = useCallback((newState: Partial<ProjectState>) => {
    setProjectState(prev => {
      const updated = { ...prev, ...newState };
      // Auto-save project state after update
      setTimeout(() => saveProject(), 0);
      return updated;
    });
  }, [saveProject]);

  // Update panel state (sizes, collapsed state)
  const updatePanelState = useCallback((panelUpdates: Partial<ProjectState['panels']>) => {
    setProjectState(prev => {
      const updated = {
        ...prev,
        panels: {
          ...prev.panels,
          ...panelUpdates
        }
      };
      // Auto-save project state after panel updates
      setTimeout(() => saveProject(), 0);
      return updated;
    });
  }, [saveProject]);

  // Add a file to recent files list
  const addRecentFile = useCallback((filePath: string) => {
    setProjectState(prev => {
      // Remove if exists already (to move it to the top)
      const filteredRecent = prev.recentFiles.filter(path => path !== filePath);

      // Add to the beginning, limit to 10 items
      const newRecentFiles = [filePath, ...filteredRecent].slice(0, 10);

      const updated = {
        ...prev,
        recentFiles: newRecentFiles
      };

      // Auto-save project state
      setTimeout(() => saveProject(), 0);

      return updated;
    });
  }, [saveProject]);

  // Update the content of a file in the lastOpenedFiles cache
  const updateFileContent = useCallback((filePath: string, content: string) => {
    setProjectState(prev => {
      const updated = {
        ...prev,
        lastOpenedFiles: {
          ...prev.lastOpenedFiles,
          [filePath]: content
        }
      };

      // Auto-save project state
      setTimeout(() => saveProject(), 0);

      return updated;
    });
  }, [saveProject]);

  // Load the last opened project on startup
  useEffect(() => {
    // Skip if we've already attempted to load
    if (didInitialLoadRef.current) {
      return;
    }

    // Mark as loaded to prevent future attempts
    didInitialLoadRef.current = true;

    const loadLastProject = async () => {
      try {
        console.log('Attempting to load last project...');

        // Check for last project path in local storage
        const lastProjectPath = localStorage.getItem('lastProjectPath');
        if (!lastProjectPath) {
          console.log('No last project path found in localStorage');
          return;
        }

        console.log(`Attempting to open project at: ${lastProjectPath}`);

        // Instead of calling openProject directly, we need to manually check for .kapi directory
        // and load the project state
        try {
          // First check if the path still exists
          const fileExplorer = getFileExplorerAPI();
          const contents = await fileExplorer.listDirectory({ path: lastProjectPath });

          // If we got here, the directory exists, now look for .kapi/project.json
          const kapiDirectory = `${lastProjectPath}/.kapi`;

          try {
            // Try to read the project.json file
            const fileExplorer = getFileExplorerAPI();
            const projectFile = await fileExplorer.readFile({
              path: `${kapiDirectory}/project.json`
            });

            if (projectFile) {
              // Parse the project state
              const loadedState = JSON.parse(projectFile);

              // Set project state
              setProjectState(loadedState);

              // Also set up the file structure
              // Get directory listing (array is guaranteed by our API wrapper)
              const fileExplorer = getFileExplorerAPI();
              const dirContents = await fileExplorer.listDirectory({
                path: lastProjectPath
              });

              // Create the root file structure
              const rootStructure: FileSystemItem = {
                name: lastProjectPath.split('/').pop() || 'Project',
                path: lastProjectPath,
                type: 'directory',
                children: dirContents
              };

              setFileStructure(rootStructure);

              setIsProjectOpen(true);
              console.log('Successfully loaded last project');
            }
          } catch (error) {
            console.warn('Could not read project file:', error);
            // If we can't read the project file, just create a new project
            createProject('Existing Project', lastProjectPath);
          }
        } catch (error) {
          console.warn('Last project path no longer accessible:', error);
        }
      } catch (err) {
        console.error('Fatal error loading last project:', err);
      }
    };

    // Execute the load operation
    loadLastProject();
  }, [createProject]);

  // Load current user
  useEffect(() => {
    const loadCurrentUser = async () => {
      const token = localStorage.getItem('token');
      const userIdStr = localStorage.getItem('currentUserId'); // Assuming user ID is stored
      if (token && userIdStr) {
        try {
          const userId = parseInt(userIdStr, 10);
          const userProfile = await communityService.fetchUserProfile(userId);
          setCurrentUser(userProfile);
          console.log('Current user loaded:', userProfile);
        } catch (error) {
          console.error('Failed to load current user profile:', error);
          // Handle error, maybe clear token/userId?
          localStorage.removeItem('token');
          localStorage.removeItem('currentUserId');
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null); // No token or userId found
      }
    };
    loadCurrentUser();
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    projectState,
    fileStructure,
    isProjectOpen,
    currentUser,
    createProject,
    openProject,
    saveProject,
    refreshFileStructure,
    updateProjectState,
    updatePanelState,
    addRecentFile,
    updateFileContent
  }), [projectState, fileStructure, isProjectOpen, currentUser, createProject, openProject, saveProject, refreshFileStructure, updateProjectState, updatePanelState, addRecentFile, updateFileContent]);

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};

// Hook to use the project context
export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};