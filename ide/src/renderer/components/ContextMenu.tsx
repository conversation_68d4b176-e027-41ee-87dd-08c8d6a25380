import React, { useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFile,
  faFolder,
  faTrash,
  faCopy,
  faCut,
  faPaste,
  faSync,
  faEdit,
  faLink
} from '@fortawesome/free-solid-svg-icons';
import '../styles/files.css';

export interface ContextMenuProps {
  x: number;
  y: number;
  isDirectory: boolean;
  isRoot?: boolean;
  onNewFile?: () => void;
  onNewFolder?: () => void;
  onDelete?: () => void;
  onRename?: () => void;
  onCopy?: () => void;
  onCut?: () => void;
  onPaste?: () => void;
  onCopyPath?: () => void;
  onRefresh?: () => void;
  onClose: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  x,
  y,
  isDirectory,
  isRoot = false,
  onNewFile,
  onNewFolder,
  onDelete,
  onRename,
  onCopy,
  onCut,
  onPaste,
  onCopyPath,
  onRefresh,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // Close the context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Adjust position if menu would go off screen
  const adjustedX = Math.min(x, window.innerWidth - 200);
  const adjustedY = Math.min(y, window.innerHeight - 300);

  const handleContainerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  return (
    <div 
      className="context-menu-container"
      ref={menuRef}
      onClick={handleContainerClick}
      onContextMenu={handleContainerClick}
      style={{ 
        top: adjustedY,
        left: adjustedX
      }}
    >
      {isDirectory && (
        <>
          <div 
            className="context-menu-item" 
            onClick={(e) => {
              e.stopPropagation();
              if (onNewFile) onNewFile();
            }}
          >
            <FontAwesomeIcon icon={faFile} className="context-menu-icon" />
            <span>New File</span>
          </div>
          <div 
            className="context-menu-item" 
            onClick={(e) => {
              e.stopPropagation();
              if (onNewFolder) onNewFolder();
            }}
          >
            <FontAwesomeIcon icon={faFolder} className="context-menu-icon" />
            <span>New Folder</span>
          </div>
          <div className="context-menu-separator"></div>
        </>
      )}

      <div 
        className="context-menu-item" 
        onClick={(e) => {
          e.stopPropagation();
          if (onCopy) onCopy();
        }}
      >
        <FontAwesomeIcon icon={faCopy} className="context-menu-icon" />
        <span>Copy</span>
      </div>

      <div 
        className="context-menu-item" 
        onClick={(e) => {
          e.stopPropagation();
          if (onCut) onCut();
        }}
      >
        <FontAwesomeIcon icon={faCut} className="context-menu-icon" />
        <span>Cut</span>
      </div>

      <div 
        className="context-menu-item" 
        onClick={(e) => {
          e.stopPropagation();
          if (onPaste) onPaste();
        }}
      >
        <FontAwesomeIcon icon={faPaste} className="context-menu-icon" />
        <span>Paste</span>
      </div>

      <div className="context-menu-separator"></div>

      <div 
        className="context-menu-item" 
        onClick={(e) => {
          e.stopPropagation();
          if (onCopyPath) onCopyPath();
        }}
      >
        <FontAwesomeIcon icon={faLink} className="context-menu-icon" />
        <span>Copy Path</span>
      </div>

      {!isRoot && (
        <>
          <div className="context-menu-separator"></div>
          <div 
            className="context-menu-item" 
            onClick={(e) => {
              e.stopPropagation();
              if (onRename) onRename();
            }}
          >
            <FontAwesomeIcon icon={faEdit} className="context-menu-icon" />
            <span>Rename</span>
          </div>
          <div 
            className="context-menu-item context-menu-item-danger" 
            onClick={(e) => {
              e.stopPropagation();
              if (onDelete) onDelete();
            }}
          >
            <FontAwesomeIcon icon={faTrash} className="context-menu-icon" />
            <span>Delete</span>
          </div>
        </>
      )}

      <div className="context-menu-separator"></div>

      <div 
        className="context-menu-item" 
        onClick={(e) => {
          e.stopPropagation();
          if (onRefresh) onRefresh();
        }}
      >
        <FontAwesomeIcon icon={faSync} className="context-menu-icon" />
        <span>Refresh</span>
      </div>
    </div>
  );
};

export default ContextMenu;