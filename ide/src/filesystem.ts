import { ipcMain, dialog, BrowserWindow } from 'electron';
import fs from 'fs';
import path from 'path';
import util from 'util';
import * as chokidar from 'chokidar';
import { spawn } from 'child_process';
import { Project, SyntaxKind, Node, ClassDeclaration, FunctionDeclaration, InterfaceDeclaration, TypeAliasDeclaration, VariableDeclaration, ImportDeclaration, ExportDeclaration } from 'ts-morph';
import { API_BASE_URL, ENABLE_CODE_ANALYSIS } from './config';

const readdir = util.promisify(fs.readdir);
const stat = util.promisify(fs.stat);
const readFile = util.promisify(fs.readFile);
const writeFile = util.promisify(fs.writeFile);
const mkdir = util.promisify(fs.mkdir);
const rm = util.promisify(fs.rm);
const rename = util.promisify(fs.rename);
const copyFile = util.promisify(fs.copyFile);

// File watcher management
let watcher: chokidar.FSWatcher | null = null;
let _watchedPath: string | null = null;

export function setupFileSystemHandlers() {
  console.log('[FileWatcher MAIN] 🚀 Setting up file system handlers...');
  
  // Clean up watcher on app quit
  process.on('beforeExit', () => {
    if (watcher) {
      watcher.close();
    }
  });
  ipcMain.handle('dialog:selectDirectory', async () => {
    const result = await dialog.showOpenDialog({ properties: ['openDirectory'] });
    if (result.canceled) return { canceled: true };

    const dirPath = result.filePaths[0];
    try {
      const contents = await getDirectoryContents(dirPath);
      return { canceled: false, dirPath, contents };
    } catch (err) {
      return { canceled: false, dirPath, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:listDirectory', async (_event, { dirPath }) => {
    try {
      const contents = await getDirectoryContents(dirPath);
      return { success: true, contents };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:readFile', async (_event, { filePath }) => {
    try {
      const content = await readFile(filePath, 'utf8');
      return { success: true, content };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:createFile', async (_event, { filePath, content = '' }) => {
    try {
      await writeFile(filePath, content);
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:createDirectory', async (_event, { dirPath }) => {
    try {
      await mkdir(dirPath, { recursive: true });
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:delete', async (_event, { path: itemPath }) => {
    try {
      await rm(itemPath, { recursive: true, force: true });
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:rename', async (_event, { oldPath, newPath }) => {
    try {
      await rename(oldPath, newPath);
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:copy', async (_event, { sourcePath, destPath }) => {
    try {
      const sourceStats = await stat(sourcePath);
      
      if (sourceStats.isDirectory()) {
        // For directories, create the destination directory and copy contents recursively
        await mkdir(destPath, { recursive: true });
        
        const items = await readdir(sourcePath);
        await Promise.all(
          items.map(async (item) => {
            const sourceItemPath = path.join(sourcePath, item);
            const destItemPath = path.join(destPath, item);
            
            const itemStats = await stat(sourceItemPath);
            if (itemStats.isDirectory()) {
              // Recursively copy directory
              await copyDirectoryRecursive(sourceItemPath, destItemPath);
            } else {
              // Copy file
              await copyFile(sourceItemPath, destItemPath);
            }
          })
        );
      } else {
        // For files, just copy the file
        await copyFile(sourcePath, destPath);
      }
      
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:move', async (_event, { sourcePath, destPath }) => {
    try {
      await rename(sourcePath, destPath);
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('fs:getDirectoryInfo', async (_event, { dirPath }) => {
    try {
      if (!dirPath) {
        return { success: false, error: 'No directory path provided' };
      }

      // Check if directory exists
      try {
        const stats = await stat(dirPath);
        if (!stats.isDirectory()) {
          return { success: false, error: 'Path is not a directory' };
        }
      } catch {
        return { success: false, error: 'Directory does not exist' };
      }

      // Get directory contents
      const files = await readdir(dirPath);

      // Get the latest modification time of any file in the directory
      let latestModTime = 0;

      for (const file of files) {
        try {
          const filePath = path.join(dirPath, file);
          const fileStats = await stat(filePath);
          const modTime = fileStats.mtimeMs;

          if (modTime > latestModTime) {
            latestModTime = modTime;
          }
        } catch (err) {
          // Skip files that can't be accessed
          console.warn(`Could not access file ${file} in directory ${dirPath}:`, err);
        }
      }

      return {
        success: true,
        modificationTime: latestModTime || Date.now(),
        fileCount: files.length
      };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  // Start watching a directory
  ipcMain.handle('fs:startWatching', async (_event, { path: dirPath }) => {
    try {
      console.log(`[FileWatcher MAIN] 🚀 START WATCHING REQUEST for: ${dirPath}`);
      
      // Close existing watcher if any
      if (watcher) {
        console.log(`[FileWatcher MAIN] 🔄 Closing existing watcher for: ${_watchedPath}`);
        await watcher.close();
        watcher = null;
      }

      // Create new watcher with proper ignore patterns
      console.log(`[FileWatcher MAIN] 🔧 Creating watcher with optimized config...`);
      watcher = chokidar.watch(dirPath, {
        persistent: true,
        ignoreInitial: true, // Ignore initial scan to avoid flood
        usePolling: false, // Use native FS events
        depth: 10, // Limit recursion depth to prevent EMFILE errors
        awaitWriteFinish: {
          stabilityThreshold: 300,
          pollInterval: 100
        },
        ignored: function(path: string) {
          // Use a function for more precise control
          const normalizedPath = path.replace(/\\/g, '/');
          
          // Ignore node_modules at any level
          if (normalizedPath.includes('/node_modules/') || normalizedPath.endsWith('/node_modules')) {
            return true;
          }
          
          // Ignore other heavy directories
          const heavyDirs = [
            '/.venv/', '/venv/', '/.env/', '/.git/', '/.kapi/',
            '/dist/', '/build/', '/coverage/', '/__pycache__/',
            '/target/', '/vendor/', '/.next/', '/.nuxt/', '/.cache/',
            '/logs/', '/tmp/', '/temp/'
          ];
          
          for (const dir of heavyDirs) {
            if (normalizedPath.includes(dir) || normalizedPath.endsWith(dir.slice(0, -1))) {
              return true;
            }
          }
          
          // Ignore specific file patterns
          const fileName = normalizedPath.split('/').pop() || '';
          const ignoreFiles = [
            '.DS_Store', 'Thumbs.db', 'package-lock.json', 'yarn.lock', 
            'pnpm-lock.yaml'
          ];
          
          if (ignoreFiles.includes(fileName)) {
            return true;
          }
          
          // Ignore file extensions
          const ignoreExtensions = [
            '.swp', '.swo', '.tmp', '.log', '.lock', '.pyc', '.pyo', '.pyd'
          ];
          
          for (const ext of ignoreExtensions) {
            if (fileName.endsWith(ext)) {
              return true;
            }
          }
          
          // Ignore temporary files and dot files (except important ones)
          if (fileName.startsWith('.') && !['.','..','.env','.gitignore','.eslintrc'].includes(fileName)) {
            return true;
          }
          
          return false;
        }
      });

      _watchedPath = dirPath;

      // Watcher configuration set up successfully

      // Set up event handlers
      const notifyChange = (eventType: string, filePath: string) => {
        // Send event to all windows (reduced logging)
        const windows = BrowserWindow.getAllWindows();
        windows.forEach((window) => {
          if (!window.isDestroyed()) {
            const eventData = { 
              type: eventType, 
              path: filePath,
              timestamp: Date.now() 
            };
            window.webContents.send('fs:fileChanged', eventData);
          }
        });
      };

      watcher
        .on('add', (path: string) => {
          notifyChange('add', path);
        })
        .on('change', (path: string) => {
          notifyChange('change', path);
        })
        .on('unlink', (path: string) => {
          notifyChange('unlink', path);
        })
        .on('addDir', (path: string) => {
          notifyChange('addDir', path);
        })
        .on('unlinkDir', (path: string) => {
          notifyChange('unlinkDir', path);
        })
        .on('error', (error: unknown) => {
          console.error('[FileWatcher MAIN] ❌ ERROR:', error);
        })
        .on('ready', () => {
          console.log(`[FileWatcher MAIN] ✅ READY! Now watching: ${dirPath}`);
        });

      console.log(`[FileWatcher MAIN] ✅ Watcher created successfully for: ${dirPath}`);
      
      return { success: true };
    } catch (err) {
      console.error('[FileWatcher] Failed to start watching:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Stop watching
  ipcMain.handle('fs:stopWatching', async () => {
    try {
      if (watcher) {
        await watcher.close();
        watcher = null;
        _watchedPath = null;
        console.log('[FileWatcher] Stopped watching');
      }
      return { success: true };
    } catch (err) {
      return { success: false, error: (err as Error).message };
    }
  });

  // Search in files
  ipcMain.handle('fs:searchInFiles', async (_event, { query, path: searchPath, caseSensitive = false, useRegex = false, wholeWord = false }) => {
    try {
      if (!query?.trim() || !searchPath) {
        return { success: false, error: 'Query and path are required' };
      }

      const results = await searchInFiles(query, searchPath, { caseSensitive, useRegex, wholeWord });
      return { success: true, results };
    } catch (err) {
      console.error('[Search] Error searching files:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Search file names
  ipcMain.handle('fs:searchFileNames', async (_event, { query, path: searchPath, caseSensitive = false }) => {
    try {
      if (!query?.trim() || !searchPath) {
        return { success: false, error: 'Query and path are required' };
      }

      const results = await searchFileNames(query, searchPath, { caseSensitive });
      return { success: true, results };
    } catch (err) {
      console.error('[Search] Error searching file names:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Initialize AST project
  ipcMain.handle('ast:initializeProject', async (_event, { path: projectPath }) => {
    try {
      if (!projectPath) {
        return { success: false, error: 'Project path is required' };
      }

      if (!astService) {
        astService = new TypeScriptASTService();
      }

      astService.initializeProject(projectPath);
      return { success: true };
    } catch (err) {
      console.error('[AST] Error initializing project:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Search symbols
  ipcMain.handle('ast:searchSymbols', async (_event, { query, caseSensitive = false, maxResults = 500 }) => {
    try {
      if (!query?.trim()) {
        return { success: false, error: 'Query is required' };
      }

      if (!astService) {
        return { success: false, error: 'AST service not initialized. Please initialize a project first.' };
      }

      const results = await astService.searchSymbols(query, { caseSensitive, maxResults });
      return { success: true, results };
    } catch (err) {
      console.error('[AST] Error searching symbols:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Refresh AST project
  ipcMain.handle('ast:refreshProject', async () => {
    try {
      if (!astService) {
        return { success: false, error: 'AST service not initialized' };
      }

      astService.refreshProject();
      return { success: true };
    } catch (err) {
      console.error('[AST] Error refreshing project:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Detect TypeScript projects in a directory
  ipcMain.handle('ast:detectProjects', async (_event, { path: rootPath }) => {
    try {
      if (!rootPath) {
        return { success: false, error: 'Root path is required' };
      }

      if (!astService) {
        astService = new TypeScriptASTService();
      }

      // Use the detection method (making it accessible)
      const detectedProjects = (astService as any).detectTypeScriptProjects(rootPath);
      return { success: true, projects: detectedProjects };
    } catch (err) {
      console.error('[AST] Error detecting projects:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  // Code Analysis IPC Handlers
  
  // Helper function to get or create project ID from path
  const getProjectIdFromPath = async (projectPath: string): Promise<number> => {
    try {
      console.log('[CodeAnalysis] Looking up project for path:', projectPath);
      
      // For development/demo purposes, generate a consistent hash-based ID
      // This allows code analysis to work without requiring backend authentication
      const projectId = Math.abs(projectPath.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0));
      
      console.log('[CodeAnalysis] Using hash-based project ID:', projectId);
      return projectId;
      
      // TODO: Enable API-based project creation once authentication is properly configured
      // const response = await fetch(`${API_BASE_URL}/projects`, {
      //   method: 'GET',
      //   headers: {
      //     'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
      //   }
      // });
      // ... rest of API implementation
      
    } catch (err) {
      console.error('Error getting project ID:', err);
      // Fallback to path hash
      return Math.abs(projectPath.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0));
    }
  };

  ipcMain.handle('code-analysis:analyzeProject', async (_event, { projectPath, includeAI = true, analysisDepth = 'detailed' }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: false, error: 'Code analysis feature is disabled' };
    }

    try {
      console.log('[CodeAnalysis] Starting project analysis for:', projectPath);
      const projectId = await getProjectIdFromPath(projectPath);
      console.log('[CodeAnalysis] Using project ID:', projectId);
      
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        },
        body: JSON.stringify({
          projectId,
          includeAI,
          analysisDepth
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('[CodeAnalysis] Analysis completed, health score:', data.overallScore);
      return { success: true, data };
    } catch (err) {
      console.error('[CodeAnalysis] Error analyzing project:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('code-analysis:analyzeFile', async (_event, { filePath, includeAI = false }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: false, error: 'Code analysis feature is disabled' };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/analyze-file`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        },
        body: JSON.stringify({
          filePath,
          includeAI
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (err) {
      console.error('[CodeAnalysis] Error analyzing file:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('code-analysis:getLatestHealth', async (_event, { projectPath }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: true, data: null }; // Return null data when disabled
    }

    try {
      const projectId = await getProjectIdFromPath(projectPath);
      
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/health/${projectId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          return { success: true, data: null }; // No analysis found yet
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (err) {
      console.error('[CodeAnalysis] Error getting latest health:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('code-analysis:explainIssue', async (_event, { issueId }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: false, error: 'Code analysis feature is disabled' };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/explain/${issueId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (err) {
      console.error('[CodeAnalysis] Error explaining issue:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('code-analysis:suggestRefactoring', async (_event, { filePath, codeSnippet }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: false, error: 'Code analysis feature is disabled' };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/suggest-refactoring`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        },
        body: JSON.stringify({
          filePath,
          codeSnippet
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (err) {
      console.error('[CodeAnalysis] Error suggesting refactoring:', err);
      return { success: false, error: (err as Error).message };
    }
  });

  ipcMain.handle('code-analysis:getFileHealth', async (_event, { projectPath, filePath }) => {
    if (!ENABLE_CODE_ANALYSIS) {
      //console.log('[CodeAnalysis] Code analysis is disabled');
      return { success: true, data: null }; // Return null data when disabled
    }

    try {
      const projectId = await getProjectIdFromPath(projectPath);
      
      // Get the latest health report for the project
      const response = await fetch(`${API_BASE_URL}/api/v1/code-analysis/health/${projectId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.KAPI_AUTH_TOKEN || 'dev-token'}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          return { success: true, data: null }; // No analysis found yet
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const healthReport = await response.json();
      
      // Find the specific file in the report
      const fileMetrics = healthReport.fileMetrics?.find((fm: any) => 
        fm.file_path === filePath || fm.file_path.endsWith(filePath)
      );
      
      if (fileMetrics) {
        return { 
          success: true, 
          data: {
            riskLevel: fileMetrics.risk_level,
            complexity: fileMetrics.complexity,
            hasErrorHandling: fileMetrics.has_error_handling,
            issues: fileMetrics.issues || []
          }
        };
      }
      
      return { success: true, data: null };
    } catch (err) {
      console.error('[CodeAnalysis] Error getting file health:', err);
      return { success: false, error: (err as Error).message };
    }
  });
}

async function getDirectoryContents(dirPath: string) {
  const items = await readdir(dirPath);
  const itemStats = await Promise.all(
    items.map(async (item) => {
      const itemPath = path.join(dirPath, item);
      const stats = await stat(itemPath);
      return {
        name: item,
        path: itemPath,
        type: stats.isDirectory() ? 'directory' : 'file',
      };
    })
  );
  return itemStats;
}

async function copyDirectoryRecursive(sourcePath: string, destPath: string) {
  await mkdir(destPath, { recursive: true });
  
  const items = await readdir(sourcePath);
  await Promise.all(
    items.map(async (item) => {
      const sourceItemPath = path.join(sourcePath, item);
      const destItemPath = path.join(destPath, item);
      
      const itemStats = await stat(sourceItemPath);
      if (itemStats.isDirectory()) {
        await copyDirectoryRecursive(sourceItemPath, destItemPath);
      } else {
        await copyFile(sourceItemPath, destItemPath);
      }
    })
  );
}

interface SearchOptions {
  caseSensitive?: boolean;
  useRegex?: boolean;
  wholeWord?: boolean;
  maxResults?: number;
}

// TypeScript AST Symbol Types
interface SymbolResult {
  id: string;
  file: string;
  name: string;
  kind: 'function' | 'class' | 'interface' | 'type' | 'variable' | 'import' | 'export';
  line: number;
  column: number;
  preview: string;
  signature?: string;
  returnType?: string;
  parameters?: string[];
  modifiers?: string[];
  namespace?: string;
}

// TypeScript AST Service
class TypeScriptASTService {
  private project: Project | null = null;
  private symbolCache: Map<string, SymbolResult[]> = new Map();
  private projectPath: string | null = null;

  initializeProject(projectPath: string): void {
    try {
      this.projectPath = projectPath;
      
      // First, try to find the best TypeScript/JavaScript project directory
      const detectedProjects = this.detectTypeScriptProjects(projectPath);
      
      let actualProjectPath = projectPath;
      if (detectedProjects.length > 0) {
        // Use the first detected project (prioritize those with tsconfig.json)
        const bestProject = detectedProjects.find(p => p.hasConfig) || detectedProjects[0];
        actualProjectPath = bestProject.path;
        console.log(`[AST] Auto-detected TypeScript project: ${path.basename(actualProjectPath)} (${bestProject.fileCount} files)`);
      }
      
      // Find tsconfig.json or create default config
      const tsconfigPath = this.findTsConfig(actualProjectPath);
      
      this.project = new Project({
        tsConfigFilePath: tsconfigPath,
        skipAddingFilesFromTsConfig: false,
        useInMemoryFileSystem: false,
      });

      const sourceFiles = this.project.getSourceFiles().length;
      const configInfo = tsconfigPath ? ` (${path.basename(tsconfigPath)})` : ' (no config)';
      console.log(`[AST] TypeScript project initialized with ${sourceFiles} files${configInfo}`);
    } catch (error) {
      console.error('[AST] Failed to initialize TypeScript project:', error);
      // Fallback: create project without tsconfig
      this.project = new Project({
        useInMemoryFileSystem: false,
      });
      this.addSourceFiles(projectPath);
    }
  }

  private findTsConfig(projectPath: string): string | undefined {
    const possiblePaths = [
      path.join(projectPath, 'tsconfig.json'),
      path.join(projectPath, 'src', 'tsconfig.json'),
      path.join(projectPath, 'client', 'tsconfig.json'),
      path.join(projectPath, 'frontend', 'tsconfig.json'),
    ];

    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        return configPath;
      }
    }
    return undefined;
  }

  private addSourceFiles(projectPath: string): void {
    if (!this.project) return;

    try {
      // Use a more conservative approach for file discovery
      this.walkAndAddFiles(projectPath, this.project);
    } catch (error) {
      console.error('[AST] Failed to add source files:', error);
    }
  }

  private walkAndAddFiles(dirPath: string, project: Project): void {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        
        // Skip ignored directories
        if (shouldIgnoreDirectory(item)) {
          continue;
        }
        
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          this.walkAndAddFiles(itemPath, project);
        } else if (stats.isFile() && this.isTypeScriptFile(itemPath)) {
          try {
            project.addSourceFileAtPath(itemPath);
          } catch (error) {
            // Skip files that can't be parsed
            console.warn(`[AST] Could not parse file ${path.basename(itemPath)}`);
          }
        }
      }
    } catch (error) {
      console.warn(`[AST] Could not read directory ${path.basename(dirPath)}`);
    }
  }

  private isTypeScriptFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);
    
    // Check if it's a TypeScript/JavaScript file
    if (!['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
      return false;
    }
    
    // Skip node_modules
    if (filePath.includes('node_modules')) {
      return false;
    }
    
    // Skip .d.ts files for now (type definitions)
    if (fileName.endsWith('.d.ts')) {
      return false;
    }
    
    // Skip test files that might cause issues
    if (fileName.includes('.test.') || fileName.includes('.spec.')) {
      return false;
    }
    
    // Valid TypeScript file (reduced logging)
    return true;
  }

  async searchSymbols(query: string, options: SearchOptions = {}): Promise<SymbolResult[]> {
    if (!this.project || !query.trim()) {
      return [];
    }

    const { caseSensitive = false, maxResults = 500 } = options;
    const results: SymbolResult[] = [];
    
    // Create search pattern
    const searchPattern = caseSensitive ? query : query.toLowerCase();
    
    try {
      const sourceFiles = this.project.getSourceFiles();
      
      for (const sourceFile of sourceFiles) {
        if (results.length >= maxResults) break;
        
        const filePath = sourceFile.getFilePath();
        const relativePath = this.projectPath ? path.relative(this.projectPath, filePath) : filePath;
        
        // Skip if this is an ignored file
        if (shouldIgnoreFile(filePath)) continue;
        
        // Extract symbols from this file
        const fileSymbols = this.extractSymbolsFromFile(sourceFile, searchPattern, caseSensitive, relativePath);
        results.push(...fileSymbols.slice(0, maxResults - results.length));
      }
    } catch (error) {
      console.error('[AST] Error during symbol search:', error);
    }

    return results;
  }

  private extractSymbolsFromFile(sourceFile: any, searchPattern: string, caseSensitive: boolean, relativePath: string): SymbolResult[] {
    const results: SymbolResult[] = [];
    
    try {
      // Functions
      sourceFile.getFunctions().forEach((func: FunctionDeclaration) => {
        const name = func.getName();
        if (name && this.matchesSearch(name, searchPattern, caseSensitive)) {
          results.push(this.createSymbolResult(func, 'function', relativePath, name));
        }
      });

      // Classes
      sourceFile.getClasses().forEach((cls: ClassDeclaration) => {
        const name = cls.getName();
        if (name && this.matchesSearch(name, searchPattern, caseSensitive)) {
          results.push(this.createSymbolResult(cls, 'class', relativePath, name));
        }
        
        // Class methods
        cls.getMethods().forEach((method) => {
          const methodName = method.getName();
          if (methodName && this.matchesSearch(methodName, searchPattern, caseSensitive)) {
            results.push(this.createSymbolResult(method, 'function', relativePath, `${name}.${methodName}`));
          }
        });
      });

      // Interfaces
      sourceFile.getInterfaces().forEach((iface: InterfaceDeclaration) => {
        const name = iface.getName();
        if (name && this.matchesSearch(name, searchPattern, caseSensitive)) {
          results.push(this.createSymbolResult(iface, 'interface', relativePath, name));
        }
      });

      // Type aliases
      sourceFile.getTypeAliases().forEach((type: TypeAliasDeclaration) => {
        const name = type.getName();
        if (name && this.matchesSearch(name, searchPattern, caseSensitive)) {
          results.push(this.createSymbolResult(type, 'type', relativePath, name));
        }
      });

      // Variables and constants
      sourceFile.getVariableDeclarations().forEach((variable: VariableDeclaration) => {
        const name = variable.getName();
        if (name && this.matchesSearch(name, searchPattern, caseSensitive)) {
          results.push(this.createSymbolResult(variable, 'variable', relativePath, name));
        }
      });

    } catch (error) {
      console.warn(`[AST] Error extracting symbols from ${relativePath}:`, error);
    }

    return results;
  }

  private matchesSearch(name: string, searchPattern: string, caseSensitive: boolean): boolean {
    const searchIn = caseSensitive ? name : name.toLowerCase();
    return searchIn.includes(searchPattern);
  }

  private createSymbolResult(node: Node, kind: SymbolResult['kind'], filePath: string, name: string): SymbolResult {
    const startPos = node.getStart();
    const sourceFile = node.getSourceFile();
    const { line, column } = sourceFile.getLineAndColumnAtPos(startPos);
    
    // Get preview text (the line containing the symbol)
    const text = sourceFile.getFullText();
    const lines = text.split('\n');
    const preview = lines[line - 1]?.trim() || '';

    // Try to get signature for functions
    let signature = '';
    let returnType = '';
    let parameters: string[] = [];
    let modifiers: string[] = [];

    try {
      if (kind === 'function') {
        const funcNode = node as any;
        if (funcNode.getParameters) {
          parameters = funcNode.getParameters().map((p: any) => p.getText());
        }
        if (funcNode.getReturnTypeNode) {
          const returnTypeNode = funcNode.getReturnTypeNode();
          returnType = returnTypeNode ? returnTypeNode.getText() : 'void';
        }
        if (funcNode.getModifiers) {
          modifiers = funcNode.getModifiers().map((m: any) => m.getText());
        }
        signature = `${name}(${parameters.join(', ')})${returnType ? ': ' + returnType : ''}`;
      } else if (kind === 'class' || kind === 'interface') {
        const typeNode = node as any;
        if (typeNode.getModifiers) {
          modifiers = typeNode.getModifiers().map((m: any) => m.getText());
        }
      }
    } catch (error) {
      // Ignore signature extraction errors
    }

    return {
      id: `${filePath}:${line}:${column}:${kind}:${name}`,
      file: filePath,
      name,
      kind,
      line,
      column,
      preview,
      signature,
      returnType,
      parameters,
      modifiers,
    };
  }

  clearCache(): void {
    this.symbolCache.clear();
  }

  refreshProject(): void {
    if (this.project && this.projectPath) {
      this.clearCache();
      this.initializeProject(this.projectPath);
    }
  }

  private detectTypeScriptProjects(rootPath: string, maxDepth: number = 2): DetectedProject[] {
    const projects: DetectedProject[] = [];
    
    try {
      this.scanForProjects(rootPath, rootPath, projects, 0, maxDepth);
      
      // Sort by priority: 1) has config, 2) more files, 3) name
      projects.sort((a, b) => {
        if (a.hasConfig !== b.hasConfig) return b.hasConfig ? 1 : -1;
        if (a.fileCount !== b.fileCount) return b.fileCount - a.fileCount;
        return a.name.localeCompare(b.name);
      });
      
      if (projects.length > 0) {
        console.log(`[AST] Found ${projects.length} TypeScript project${projects.length > 1 ? 's' : ''}`);
      }
      return projects;
    } catch (error) {
      console.error('[AST] Error detecting TypeScript projects:', error);
      return [];
    }
  }

  private scanForProjects(currentPath: string, rootPath: string, projects: DetectedProject[], depth: number, maxDepth: number): void {
    if (depth > maxDepth) return;
    
    try {
      const items = fs.readdirSync(currentPath);
      let hasTypeScriptFiles = false;
      let hasConfig = false;
      let fileCount = 0;
      
      // Check current directory
      for (const item of items) {
        const itemPath = path.join(currentPath, item);
        
        try {
          const stats = fs.statSync(itemPath);
          
          if (stats.isFile()) {
            // Check for TypeScript config files
            if (item === 'tsconfig.json' || item === 'package.json') {
              hasConfig = true;
            }
            
            // Count TypeScript files
            if (this.isTypeScriptFileForDetection(itemPath)) {
              hasTypeScriptFiles = true;
              fileCount++;
            }
          }
        } catch (error) {
          // Skip files that can't be accessed
          continue;
        }
      }
      
      // If this directory has TypeScript files, add it as a project
      if (hasTypeScriptFiles && fileCount >= 2) { // At least 2 TS files to be considered a project
        const projectName = path.basename(currentPath);
        projects.push({
          path: currentPath,
          hasConfig,
          fileCount,
          name: projectName
        });
        
        // Project detected (logging reduced for brevity)
      }
      
      // Recursively scan subdirectories
      if (depth < maxDepth) {
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          
          try {
            const stats = fs.statSync(itemPath);
            
            if (stats.isDirectory() && !shouldIgnoreDirectory(item)) {
              this.scanForProjects(itemPath, rootPath, projects, depth + 1, maxDepth);
            }
          } catch (error) {
            // Skip directories that can't be accessed
            continue;
          }
        }
      }
    } catch (error) {
      console.warn(`[AST] Could not scan directory ${currentPath}:`, error);
    }
  }

  private isTypeScriptFileForDetection(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);
    
    // Check if it's a TypeScript/JavaScript file
    if (!['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
      return false;
    }
    
    // Skip test files and type definitions for project detection
    if (fileName.includes('.test.') || fileName.includes('.spec.') || fileName.endsWith('.d.ts')) {
      return false;
    }
    
    return true;
  }
}

// Project detection result interface
interface DetectedProject {
  path: string;
  hasConfig: boolean;
  fileCount: number;
  name: string;
}

// Global AST service instance
let astService: TypeScriptASTService | null = null;

interface SearchResult {
  id: string;
  file: string;
  line: number;
  column: number;
  match: string;
  preview: string;
  matchStart: number;
  matchEnd: number;
}

async function searchInFiles(query: string, searchPath: string, options: SearchOptions = {}): Promise<SearchResult[]> {
  const { caseSensitive = false, useRegex = false, wholeWord = false, maxResults = 1000 } = options;
  
  // Try to use ripgrep first, fall back to basic search
  try {
    return await searchWithRipgrep(query, searchPath, options);
  } catch (error) {
    console.log('[Search] Ripgrep not available, falling back to basic search');
    return await basicSearch(query, searchPath, options);
  }
}

async function searchWithRipgrep(query: string, searchPath: string, options: SearchOptions): Promise<SearchResult[]> {
  return new Promise((resolve, reject) => {
    const args = ['--json', '--no-heading', '--with-filename', '--line-number', '--column'];
    
    if (!options.caseSensitive) {
      args.push('--ignore-case');
    }
    
    if (options.wholeWord) {
      args.push('--word-regexp');
    }
    
    if (!options.useRegex) {
      args.push('--fixed-strings');
    }
    
    args.push('--', query, searchPath);
    
    const rg = spawn('rg', args, { cwd: searchPath });
    let output = '';
    let errorOutput = '';
    
    rg.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    rg.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    rg.on('close', (code) => {
      if (code === 0 || code === 1) { // 0 = matches found, 1 = no matches (not an error)
        try {
          const results = parseRipgrepOutput(output, query);
          resolve(results.slice(0, options.maxResults || 1000));
        } catch (err) {
          reject(new Error(`Failed to parse ripgrep output: ${err}`));
        }
      } else {
        reject(new Error(`Ripgrep failed with code ${code}: ${errorOutput}`));
      }
    });
    
    rg.on('error', (err) => {
      reject(err);
    });
  });
}

function parseRipgrepOutput(output: string, query: string): SearchResult[] {
  const results: SearchResult[] = [];
  const lines = output.trim().split('\n').filter(line => line.trim());
  
  for (const line of lines) {
    try {
      const data = JSON.parse(line);
      
      if (data.type === 'match') {
        const match = data.data;
        const relativePath = path.relative(process.cwd(), match.path.text);
        
        for (const submatch of match.submatches) {
          const result: SearchResult = {
            id: `${relativePath}:${match.line_number}:${submatch.start}`,
            file: relativePath,
            line: match.line_number,
            column: submatch.start + 1, // ripgrep uses 0-based columns
            match: submatch.match.text,
            preview: match.lines.text,
            matchStart: submatch.start,
            matchEnd: submatch.end
          };
          
          results.push(result);
        }
      }
    } catch (err) {
      // Skip invalid JSON lines
      continue;
    }
  }
  
  return results;
}

async function basicSearch(query: string, searchPath: string, options: SearchOptions): Promise<SearchResult[]> {
  const results: SearchResult[] = [];
  const { caseSensitive = false, useRegex = false, wholeWord = false, maxResults = 1000 } = options;
  
  // Create search pattern
  let pattern: RegExp;
  try {
    let regexQuery = useRegex ? query : escapeRegExp(query);
    
    if (wholeWord) {
      regexQuery = `\\b${regexQuery}\\b`;
    }
    
    const flags = caseSensitive ? 'g' : 'gi';
    pattern = new RegExp(regexQuery, flags);
  } catch (err) {
    throw new Error(`Invalid search pattern: ${err}`);
  }
  
  // Walk directory tree
  await walkDirectory(searchPath, async (filePath: string) => {
    if (results.length >= maxResults) return;
    
    // Skip binary files and common ignore patterns
    if (shouldIgnoreFile(filePath)) return;
    
    try {
      const content = await readFile(filePath, 'utf8');
      const lines = content.split('\n');
      const relativePath = path.relative(searchPath, filePath);
      
      for (let lineNum = 0; lineNum < lines.length; lineNum++) {
        if (results.length >= maxResults) break;
        
        const line = lines[lineNum];
        let match;
        
        while ((match = pattern.exec(line)) !== null) {
          const result: SearchResult = {
            id: `${relativePath}:${lineNum + 1}:${match.index}`,
            file: relativePath,
            line: lineNum + 1,
            column: match.index + 1,
            match: match[0],
            preview: line,
            matchStart: match.index,
            matchEnd: match.index + match[0].length
          };
          
          results.push(result);
          
          if (results.length >= maxResults) break;
          
          // Prevent infinite loop with zero-width matches
          if (match.index === pattern.lastIndex) {
            pattern.lastIndex++;
          }
        }
        
        // Reset regex lastIndex for next line
        pattern.lastIndex = 0;
      }
    } catch (err) {
      // Skip files that can't be read
      console.warn(`[Search] Could not read file ${filePath}:`, err);
    }
  });
  
  return results;
}

async function walkDirectory(dirPath: string, fileCallback: (filePath: string) => Promise<void>): Promise<void> {
  try {
    const items = await readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      
      // Skip common ignore patterns
      if (shouldIgnoreDirectory(item)) continue;
      
      try {
        const stats = await stat(itemPath);
        
        if (stats.isDirectory()) {
          await walkDirectory(itemPath, fileCallback);
        } else if (stats.isFile()) {
          await fileCallback(itemPath);
        }
      } catch (err) {
        // Skip items that can't be accessed
        continue;
      }
    }
  } catch (err) {
    // Skip directories that can't be read
    return;
  }
}

function shouldIgnoreDirectory(name: string): boolean {
  const ignorePatterns = [
    'node_modules',
    '.git',
    '.kapi',
    '.vscode',
    '.idea',
    'dist',
    'build',
    'coverage',
    '.next',
    '.nuxt',
    '__pycache__',
    '.pytest_cache',
    'vendor',
    'target'
  ];
  
  return ignorePatterns.includes(name) || name.startsWith('.');
}

function shouldIgnoreFile(filePath: string): boolean {
  const ext = path.extname(filePath).toLowerCase();
  const name = path.basename(filePath);
  
  // Skip binary files and common ignore patterns
  const binaryExtensions = [
    '.exe', '.dll', '.so', '.dylib', '.bin', '.obj', '.o',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
    '.mp3', '.mp4', '.avi', '.mov', '.wav', '.flac',
    '.zip', '.tar', '.gz', '.rar', '.7z',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
  ];
  
  const ignoreFiles = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
  ];
  
  return binaryExtensions.includes(ext) || 
         ignoreFiles.includes(name) ||
         name.startsWith('.') ||
         name.endsWith('.log') ||
         name.endsWith('.lock');
}

function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

interface FileNameResult {
  id: string;
  file: string;
  name: string;
  directory: string;
  type: 'file' | 'directory';
}

async function searchFileNames(query: string, searchPath: string, options: { caseSensitive?: boolean; maxResults?: number } = {}): Promise<FileNameResult[]> {
  const { caseSensitive = false, maxResults = 500 } = options;
  const results: FileNameResult[] = [];
  
  // Create search pattern for file names
  let pattern: RegExp;
  try {
    const regexQuery = escapeRegExp(query);
    const flags = caseSensitive ? 'g' : 'gi';
    pattern = new RegExp(regexQuery, flags);
  } catch (err) {
    throw new Error(`Invalid search pattern: ${err}`);
  }
  
  // Walk directory tree and match file/folder names
  await walkDirectoryForNames(searchPath, searchPath, async (itemPath: string, itemName: string, isDirectory: boolean) => {
    if (results.length >= maxResults) return;
    
    // Skip common ignore patterns
    if (shouldIgnoreDirectory(itemName) && isDirectory) return;
    if (shouldIgnoreFile(itemPath) && !isDirectory) return;
    
    // Test if the file/folder name matches
    if (pattern.test(itemName)) {
      const relativePath = path.relative(searchPath, itemPath);
      const directory = path.dirname(relativePath);
      
      const result: FileNameResult = {
        id: `${relativePath}:name`,
        file: relativePath,
        name: itemName,
        directory: directory === '.' ? '' : directory,
        type: isDirectory ? 'directory' : 'file'
      };
      
      results.push(result);
    }
  });
  
  return results;
}

async function walkDirectoryForNames(
  dirPath: string, 
  rootPath: string, 
  itemCallback: (itemPath: string, itemName: string, isDirectory: boolean) => Promise<void>
): Promise<void> {
  try {
    const items = await readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      
      // Skip common ignore patterns at directory level
      if (shouldIgnoreDirectory(item)) continue;
      
      try {
        const stats = await stat(itemPath);
        
        // Call callback for both files and directories
        await itemCallback(itemPath, item, stats.isDirectory());
        
        // Recurse into directories
        if (stats.isDirectory()) {
          await walkDirectoryForNames(itemPath, rootPath, itemCallback);
        }
      } catch (err) {
        // Skip items that can't be accessed
        continue;
      }
    }
  } catch (err) {
    // Skip directories that can't be read
    return;
  }
}
